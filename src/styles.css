@import '@angular/material/prebuilt-themes/indigo-pink.css';
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* You can add global styles to this file, and also import other style files */

/* Configurações globais */
* {
  box-sizing: border-box;
}

html, body {
  height: 100%;
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

body {
  background-color: #F7F7F7;
}

/* Melhorias no Material Design */
.mat-mdc-menu-panel {
  border-radius: 12px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15) !important;
}

.mat-mdc-menu-item {
  border-radius: 8px !important;
  margin: 2px 8px !important;
}

.mat-mdc-menu-item:hover {
  background-color: #F7F7F7 !important;
}

/* Melhorias nos botões */
.mat-mdc-button, .mat-mdc-raised-button, .mat-mdc-fab, .mat-mdc-mini-fab {
  border-radius: 8px !important;
}

/* Melhorias nos cards */
.mat-mdc-card {
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

/* Melhorias nos inputs */
.mat-mdc-form-field {
  border-radius: 8px !important;
}

/* Scrollbar personalizada */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Container principal da tela */
.container-base-telas {
  background-color: var(--mat-background-500);
  min-height: 100vh;
  font-family: "Roboto", "Noto Sans", sans-serif;
  color: var(--mat-text-primary);
}

/* Layout principal da tela*/
main {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1.5rem;
}
