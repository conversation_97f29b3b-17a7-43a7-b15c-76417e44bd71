import { CanActivateFn, Router } from '@angular/router';
import { Auth } from '../services/auth';
import {inject} from '@angular/core';

export const authGuard: CanActivateFn = (route, state) => {
  const authService = inject(Auth);
  const router = inject(Router);

  // Verificar se o token existe e não está expirado
  if (authService.getToken()) {
    return true;
  } else {
    router.navigateByUrl('/login');
    return false;
  }
};
