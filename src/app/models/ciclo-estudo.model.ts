import {DisciplinaModel} from './disciplina.model';

export interface CicloEstudoModel {
  id?: number;
  nome: string;
  disciplinas: Array<CicloEstudoDisciplinaModel>;
  horasSemanaDisponiveis?: number;
  diasSemana?: string;
  minutosDuracaoMaximaPorSessao?: number;
}

export interface CicloEstudoDisciplinaModel {
  id?: number;
  disciplina: DisciplinaModel;
  ordem: number;
  tempoEstudoMeta: string;
  cor?: string;
  peso?: number;
  nivelConhecimento?: number;
}

export class CicloEstudoAtualModel {
  cicloEstudoId: number = 0;
  nomeCicloEstudo: string = '';
  disciplinas: DisciplinaEstudoModel[] = [];
}

export interface DisciplinaEstudoModel {
  cicloEstudoDisciplinaId: number;
  ordem: number;
  disciplinaId: number;
  nomeDisciplina: string;
  tempoEstudoMeta: string;
  tempoEstudado: string;
  dataUltimoEstudo: string;
}

export class RegistroEstudoModel {
  dataRegistro?: any;
  tempoEstudado?: any;
  descricaoEstudo?: string;
  cicloEstudoDisciplinaId?: number;
  gerarRevisoesEspacadas?: boolean;
}
