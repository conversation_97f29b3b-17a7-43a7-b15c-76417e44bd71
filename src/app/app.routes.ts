import { Routes } from '@angular/router';
import { Login } from './pages/login/login';
import { Disciplinas } from './pages/disciplinas/disciplinas';
import { authGuard } from './guards/auth-guard';
import {MesaDeEstudo} from './pages/mesa-de-estudo/mesa-de-estudo.component';
import {Menu} from './pages/menu/menu';
import { ProfileComponent } from './pages/profile/profile';
import { DashboardComponent } from './pages/dashboard/dashboard';
import { PlanejamentoComponent } from './pages/planejamento/planejamento.component';

export const routes: Routes = [
  { path: 'login', component: Login },
  {
    path: '',
    component: Menu,
    canActivate: [authGuard],
    children: [
      { path: 'dashboard', component: DashboardComponent },
      { path: 'mesa-de-estudo', component: MesaDeEstudo },
      { path: 'disciplinas', component: Disciplinas },
      { path: 'profile', component: ProfileComponent },
      { path: 'planejamento', component: PlanejamentoComponent },
      { path: '', redirectTo: 'dashboard', pathMatch: 'full' }
    ]
  }
];
