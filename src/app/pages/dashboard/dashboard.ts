import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { Router } from '@angular/router';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatIconModule, MatButtonModule],
  template: `
    <div class="dashboard-container">
      <div class="welcome-section">
        <h1>Bem-vindo ao Estudo Organizado</h1>
        <p>Gerencie seus estudos de forma eficiente e organizada</p>
      </div>

      <div class="quick-actions">
        <mat-card class="action-card" (click)="navigateTo('mesa-de-estudo')">
          <mat-card-content>
            <mat-icon class="action-icon">desktop_windows</mat-icon>
            <h3>Mesa de Estudo</h3>
            <p>Inicie uma sessão de estudo focada</p>
          </mat-card-content>
        </mat-card>

        <mat-card class="action-card" (click)="navigateTo('disciplinas')">
          <mat-card-content>
            <mat-icon class="action-icon">book</mat-icon>
            <h3>Disciplinas</h3>
            <p>Organize suas disciplinas</p>
          </mat-card-content>
        </mat-card>

        <mat-card class="action-card" (click)="navigateTo('planejamento')">
          <mat-card-content>
            <mat-icon class="action-icon">loop</mat-icon>
            <h3>Planejamento</h3>
            <p>Planeje seus estudos</p>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  `,
  styles: [`
    .dashboard-container {
      padding: 24px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .welcome-section {
      text-align: center;
      margin-bottom: 48px;
      padding: 32px;
      background: linear-gradient(135deg, #4A90E2, #50E3C2);
      border-radius: 16px;
      color: white;
    }

    .welcome-section h1 {
      font-size: 2.5rem;
      font-weight: 600;
      margin-bottom: 16px;
      margin-top: 0;
    }

    .welcome-section p {
      font-size: 1.1rem;
      opacity: 0.9;
      margin: 0;
    }

    .quick-actions {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 24px;
    }

    .action-card {
      cursor: pointer;
      transition: all 0.3s ease;
      border: none;
      background: white;
      border-radius: 16px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .action-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    .action-card mat-card-content {
      padding: 32px;
      text-align: center;
    }

    .action-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: #4A90E2;
      margin-bottom: 16px;
    }

    .action-card h3 {
      font-size: 1.5rem;
      font-weight: 600;
      color: #333333;
      margin-bottom: 12px;
      margin-top: 0;
    }

    .action-card p {
      color: #666666;
      margin: 0;
      line-height: 1.5;
    }

    @media (max-width: 768px) {
      .dashboard-container {
        padding: 16px;
      }

      .welcome-section {
        padding: 24px;
      }

      .welcome-section h1 {
        font-size: 2rem;
      }

      .quick-actions {
        grid-template-columns: 1fr;
        gap: 16px;
      }
    }
  `]
})
export class DashboardComponent {
  constructor(private router: Router) {}

  navigateTo(route: string) {
    this.router.navigate([route]);
  }
}
