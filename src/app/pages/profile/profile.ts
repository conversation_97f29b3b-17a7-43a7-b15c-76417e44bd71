import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, AbstractControl, ValidationErrors, ReactiveFormsModule } from '@angular/forms';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { UserProfileService } from '../../services/user-profile.service';
import { UserProfile, UserProfileUpdate } from '../../models/user-profile.model';
import { ConfiguracaoService } from '../../services/configuracao.service';
import { Configuracao } from '../../models/configuracao.model';
import { forkJoin } from 'rxjs';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    MatSnackBarModule
  ],
  templateUrl: './profile.html',
  styleUrls: ['./profile.css']
})
export class ProfileComponent implements OnInit {
  profileForm: FormGroup;
  loading = false;
  userProfile: UserProfile | null = null;
  configuracao: Configuracao | null = null;
  private readonly intervalosPattern = /^\s*\d+(\s*,\s*\d+)*\s*$/;
  profileImageUrl: SafeUrl | string = 'assets/default-avatar.png';
  selectedFile: File | null = null;

  constructor(
    private fb: FormBuilder,
    private userProfileService: UserProfileService,
    private configuracaoService: ConfiguracaoService,
    private snackBar: MatSnackBar,
    private router: Router,
    private sanitizer: DomSanitizer
  ) {
    this.profileForm = this.fb.group({
      name: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      currentPassword: [''],
      newPassword: ['', [Validators.minLength(6)]],
      confirmPassword: [''],
      intervalosRevisao: ['', [Validators.required, Validators.pattern(this.intervalosPattern)]]
    }, { validators: this.passwordMatchValidator });
  }

  ngOnInit(): void {
    this.loadInitialData();
  }

  loadInitialData(): void {
    this.loading = true;
    forkJoin({
      profile: this.userProfileService.getUserProfile(),
      configuracao: this.configuracaoService.getConfiguration()
    }).subscribe({
      next: ({ profile, configuracao }) => {
        this.userProfile = profile;
        this.configuracao = configuracao;

        this.profileForm.patchValue({
          name: profile.name,
          email: profile.email,
          intervalosRevisao: configuracao.intervalosRevisao
        });
        
        if (profile.foto) {
          const objectURL = 'data:image/jpeg;base64,' + profile.foto;
          this.profileImageUrl = this.sanitizer.bypassSecurityTrustUrl(objectURL);
        }

        this.loading = false;
      },
      error: (error) => {
        console.error('Erro ao carregar dados iniciais:', error);
        this.snackBar.open('Erro ao carregar dados do perfil.', 'Fechar', {
          duration: 3000
        });
        this.loading = false;
      }
    });
  }

  passwordMatchValidator(control: AbstractControl): ValidationErrors | null {
    const newPassword = control.get('newPassword');
    const confirmPassword = control.get('confirmPassword');

    if (newPassword && confirmPassword && newPassword.value !== confirmPassword.value) {
      return { passwordMismatch: true };
    }

    return null;
  }

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.selectedFile = file;
      this.profileImageUrl = this.sanitizer.bypassSecurityTrustUrl(URL.createObjectURL(file));
    }
  }

  onSubmit(): void {
    if (this.profileForm.valid) {
      this.loading = true;

      const formValue = this.profileForm.value;
      const updateData: UserProfileUpdate = {
        name: formValue.name,
        email: formValue.email
      };

      if (formValue.newPassword && formValue.newPassword.trim()) {
        updateData.currentPassword = formValue.currentPassword;
        updateData.newPassword = formValue.newPassword;
      }

      const configUpdate: Configuracao = {
        intervalosRevisao: formValue.intervalosRevisao
      };

      const profileUpdate$ = this.userProfileService.updateUserProfile(updateData, this.selectedFile ?? undefined);
      const configUpdate$ = this.configuracaoService.updateConfiguration(configUpdate);

      forkJoin({
        profile: profileUpdate$,
        config: configUpdate$
      }).subscribe({
        next: ({ profile, config }) => {
          this.userProfile = profile;
          this.configuracao = config;
          
          if (profile.foto) {
            const objectURL = 'data:image/jpeg;base64,' + profile.foto;
            this.profileImageUrl = this.sanitizer.bypassSecurityTrustUrl(objectURL);
          }

          this.snackBar.open('Perfil e configurações atualizados com sucesso!', 'Fechar', {
            duration: 3000
          });

          this.profileForm.patchValue({
            currentPassword: '',
            newPassword: '',
            confirmPassword: ''
          });
          this.profileForm.markAsPristine();
          this.loading = false;
        },
        error: (error) => {
          console.error('Erro ao atualizar dados:', error);
          let errorMessage = 'Erro ao atualizar dados';

          if (error.error && error.error.message) {
            errorMessage = error.error.message;
          } else if (error.status === 400) {
            errorMessage = 'Dados inválidos. Verifique as informações fornecidas.';
          }

          this.snackBar.open(errorMessage, 'Fechar', {
            duration: 5000
          });
          this.loading = false;
        }
      });
    }
  }

  cancel(): void {
    this.router.navigate(['/mesa-de-estudo']);
  }
}
