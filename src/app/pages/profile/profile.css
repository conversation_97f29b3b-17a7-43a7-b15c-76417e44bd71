.profile-container {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.profile-card {
  margin-top: 20px;
}

.profile-card mat-card-header {
  margin-bottom: 20px;
}

.profile-card mat-card-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 24px;
  color: #333;
}

.profile-picture-container {
  position: relative;
  width: 150px;
  height: 150px;
  margin: 0 auto 20px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #f0f0f0;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.profile-picture-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: filter 0.3s ease;
}

.profile-picture-container:hover .profile-picture-preview {
  filter: brightness(0.7);
}

.upload-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0,0,0,0.6);
  color: white;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.profile-picture-container:hover .upload-button {
  opacity: 1;
}

.form-row {
  margin-bottom: 20px;
}

.full-width {
  width: 100%;
}

.divider {
  margin: 30px 0;
}

.config-section h3 {
  color: #666;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 500;
}

.password-section {
  margin-top: 20px;
}

.password-section h3 {
  color: #666;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 500;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.form-actions button {
  min-width: 120px;
}

.form-actions button[type="submit"] {
  display: flex;
  align-items: center;
  gap: 8px;
}

@media (max-width: 768px) {
  .profile-container {
    padding: 10px;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .form-actions button {
    width: 100%;
  }
} 