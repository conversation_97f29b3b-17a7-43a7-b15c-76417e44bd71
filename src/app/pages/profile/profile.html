<div class="profile-container">
  <mat-card class="profile-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>account_circle</mat-icon>
        Perfil do Usuário
      </mat-card-title>
    </mat-card-header>

    <mat-card-content>
      <form [formGroup]="profileForm" (ngSubmit)="onSubmit()">
        <div class="profile-picture-container">
          <img [src]="profileImageUrl" alt="Foto do Perfil" class="profile-picture-preview">
          <button mat-icon-button type="button" (click)="fileInput.click()" class="upload-button">
            <mat-icon>photo_camera</mat-icon>
          </button>
          <input hidden type="file" #fileInput (change)="onFileSelected($event)">
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Nome</mat-label>
            <input matInput formControlName="name" placeholder="Digite seu nome">
            <mat-error *ngIf="profileForm.get('name')?.hasError('required')">
              Nome é obrigatório
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Email</mat-label>
            <input matInput formControlName="email" type="email" placeholder="Digite seu email">
            <mat-error *ngIf="profileForm.get('email')?.hasError('required')">
              Email é obrigatório
            </mat-error>
            <mat-error *ngIf="profileForm.get('email')?.hasError('email')">
              Email inválido
            </mat-error>
          </mat-form-field>
        </div>

        <mat-divider class="divider"></mat-divider>

        <div class="config-section">
          <h3>Configurações de Revisão</h3>
          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Intervalos de Revisão (dias)</mat-label>
              <input matInput formControlName="intervalosRevisao" placeholder="Ex: 1,3,7,14,30">
              <mat-hint>Separe os dias por vírgula</mat-hint>
              <mat-error *ngIf="profileForm.get('intervalosRevisao')?.hasError('required')">
                Os intervalos são obrigatórios.
              </mat-error>
              <mat-error *ngIf="profileForm.get('intervalosRevisao')?.hasError('pattern')">
                Formato inválido. Use números separados por vírgula.
              </mat-error>
            </mat-form-field>
          </div>
        </div>

        <mat-divider class="divider"></mat-divider>

        <div class="password-section">
          <h3>Alterar Senha (opcional)</h3>
          
          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Senha Atual</mat-label>
              <input matInput formControlName="currentPassword" type="password" placeholder="Digite sua senha atual">
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Nova Senha</mat-label>
              <input matInput formControlName="newPassword" type="password" placeholder="Digite a nova senha">
              <mat-error *ngIf="profileForm.get('newPassword')?.hasError('minlength')">
                A senha deve ter pelo menos 6 caracteres
              </mat-error>
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Confirmar Nova Senha</mat-label>
              <input matInput formControlName="confirmPassword" type="password" placeholder="Confirme a nova senha">
              <mat-error *ngIf="profileForm.get('confirmPassword')?.hasError('passwordMismatch')">
                As senhas não coincidem
              </mat-error>
            </mat-form-field>
          </div>
        </div>

        <div class="form-actions">
          <button mat-button type="button" (click)="cancel()" [disabled]="loading">
            Cancelar
          </button>
          <button mat-raised-button color="primary" type="submit" [disabled]="loading || !profileForm.valid">
            <mat-spinner diameter="20" *ngIf="loading"></mat-spinner>
            <span *ngIf="!loading">Salvar Alterações</span>
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>
</div> 