import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { FormsModule } from '@angular/forms';
import { DisciplinaEstudoModel } from '../../models/ciclo-estudo.model';
import { MAT_DATE_FORMATS, DateAdapter } from '@angular/material/core';
import { MomentDateAdapter } from '@angular/material-moment-adapter';
import * as moment from 'moment';

export interface RegistroEstudoData {
  dataEstudo: Date;
  tempoEstudo: string;
  descricao: string;
  gerarRevisoesEspacadas: boolean;
}

// Configuração do formato de data brasileiro
export const MY_DATE_FORMATS = {
  parse: {
    dateInput: 'DD/MM/YYYY',
  },
  display: {
    dateInput: 'DD/MM/YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};

@Component({
  selector: 'app-registro-estudo-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatCheckboxModule,
    FormsModule
  ],
  providers: [
    { provide: DateAdapter, useClass: MomentDateAdapter },
    { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMATS }
  ],
  template: `
    <h2 mat-dialog-title>Registrar Estudo</h2>
    <mat-dialog-content>
      <div style="margin-bottom: 16px;">
        <strong>Disciplina:</strong> {{ data.disciplinaEstudo.nomeDisciplina }}
      </div>
      
      <mat-form-field appearance="outline" style="width: 100%; margin-bottom: 16px;">
        <mat-label>Data do Estudo</mat-label>
        <input matInput [matDatepicker]="picker" [(ngModel)]="registro.dataEstudo" required>
        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
        <mat-datepicker #picker></mat-datepicker>
      </mat-form-field>

      <mat-form-field appearance="outline" style="width: 100%; margin-bottom: 16px;">
        <mat-label>Tempo de Estudo (HH:MM:SS)</mat-label>
        <input matInput [(ngModel)]="registro.tempoEstudo" 
               placeholder="00:00:00" 
               (input)="formatarTempo($event)"
               maxlength="8"
               required>
        <mat-hint>Formato: HH:MM:SS (Horas: 0-23, Minutos: 0-59, Segundos: 0-59)</mat-hint>
      </mat-form-field>

      <mat-form-field appearance="outline" style="width: 100%; margin-bottom: 16px;">
        <mat-label>Descrição do Estudo</mat-label>
        <textarea matInput [(ngModel)]="registro.descricao" 
                  rows="3" 
                  placeholder="Descreva o que foi estudado..."
                  required></textarea>
      </mat-form-field>

      <div class="checkbox-section">
        <mat-checkbox [(ngModel)]="registro.gerarRevisoesEspacadas">
          Gerar revisões espaçadas
        </mat-checkbox>
        <div class="checkbox-hint">
          <small>Serão criadas revisões automáticas em intervalos otimizados para melhor memorização</small>
        </div>
      </div>
    </mat-dialog-content>
    
    <mat-dialog-actions align="end">
      <button mat-button (click)="onCancel()">Cancelar</button>
      <button mat-raised-button color="primary" 
              [disabled]="!isFormValid()"
              (click)="onConfirm()">
        Registrar
      </button>
    </mat-dialog-actions>
  `,
  styles: [`
    mat-dialog-content {
      min-width: 400px;
    }
    .checkbox-section {
      width: 100%;
      margin: 8px 0;
      display: flex;
      flex-direction: column;
      gap: 4px;
    }
    .checkbox-hint {
      margin-left: 24px;
      color: #666;
    }
  `]
})
export class RegistroEstudoDialog {
  registro: RegistroEstudoData = {
    dataEstudo: new Date(),
    tempoEstudo: '',
    descricao: '',
    gerarRevisoesEspacadas: false
  };

  constructor(
    public dialogRef: MatDialogRef<RegistroEstudoDialog>,
    @Inject(MAT_DIALOG_DATA) public data: { disciplinaEstudo: DisciplinaEstudoModel }
  ) {}

  onCancel(): void {
    this.dialogRef.close();
  }

  onConfirm(): void {
    if (this.isFormValid()) {
      this.dialogRef.close(this.registro);
    }
  }

  formatarTempo(event: any): void {
    let value = event.target.value.replace(/\D/g, ''); // Remove tudo que não é dígito
    
    if (value.length > 6) {
      value = value.substring(0, 6);
    }
    
    // Se não há valor, limpa o campo
    if (value.length === 0) {
      this.registro.tempoEstudo = '';
      return;
    }
    
    // Extrai os dígitos para cada seção
    let horas = value.substring(0, Math.min(2, value.length));
    let minutos = value.length >= 3 ? value.substring(2, Math.min(4, value.length)) : '';
    let segundos = value.length >= 5 ? value.substring(4, Math.min(6, value.length)) : '';
    
    // Valida e corrige valores inválidos
    let horasNum = parseInt(horas);
    if (horasNum > 23) {
      horas = '23';
    }
    
    if (minutos) {
      let minutosNum = parseInt(minutos);
      if (minutosNum > 59) {
        minutos = '59';
      }
    }
    
    if (segundos) {
      let segundosNum = parseInt(segundos);
      if (segundosNum > 59) {
        segundos = '59';
      }
    }
    
    // Formata o resultado baseado no número de dígitos digitados
    let resultado = '';
    
    if (value.length <= 2) {
      // Apenas horas foram digitadas
      resultado = horas;
    } else if (value.length <= 4) {
      // Horas e minutos foram digitados
      resultado = horas + ':' + minutos;
    } else {
      // Horas, minutos e segundos foram digitados
      resultado = horas + ':' + minutos + ':' + segundos;
    }
    
    this.registro.tempoEstudo = resultado;
  }

  isFormValid(): boolean {
    if (!this.registro.dataEstudo || !this.registro.tempoEstudo || !this.registro.descricao) {
      return false;
    }
    
    // Valida formato HH:MM:SS completo
    const tempoRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/;
    if (!tempoRegex.test(this.registro.tempoEstudo)) {
      return false;
    }
    
    // Valida que não é 00:00:00
    if (this.registro.tempoEstudo === '00:00:00') {
      return false;
    }
    
    return true;
  }
} 