import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { FormBuilder, FormGroup, FormArray, Validators, ReactiveFormsModule, FormsModule, AbstractControl, ValidationErrors } from '@angular/forms';
import { DisciplinaService } from '../../services/disciplina.service';
import { CicloEstudoService } from '../../services/ciclo-estudo.service';
import { PlanejamentoService, PlanejamentoModel } from '../../services/planejamento.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { DisciplinaModel } from '../../models/disciplina.model';
import { CicloEstudoModel } from '../../models/ciclo-estudo.model';

@Component({
  selector: 'app-planejamento-modal',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatCheckboxModule,
    MatProgressBarModule,
    MatSnackBarModule,
    ReactiveFormsModule,
    FormsModule
  ],
  templateUrl: './planejamento-modal.component.html',
  styleUrl: './planejamento-modal.component.css'
})
export class PlanejamentoModalComponent {
  currentStep = 1;
  step1Form!: FormGroup;
  step2Form!: FormGroup;
  revisaoForm!: FormGroup;
  novaDisciplinaForm!: FormGroup;

  step1Completed = false;
  step2Completed = false;
  step3Completed = false;

  disciplinas: DisciplinaModel[] = [];
  disciplinasSelecionadas: any[] = [];
  cicloDeEstudo: any = null;

  loadingSugestao = false;
  erroSugestao = '';
  showNovaDisciplinaForm = false;

  diasSemanaList = [
    { label: 'Dom', value: 'DOM' },
    { label: 'Seg', value: 'SEG' },
    { label: 'Ter', value: 'TER' },
    { label: 'Qua', value: 'QUA' },
    { label: 'Qui', value: 'QUI' },
    { label: 'Sex', value: 'SEX' },
    { label: 'Sáb', value: 'SAB' }
  ];

  intervalosPadrao = [1, 3, 7, 15, 30];

  constructor(
    public dialogRef: MatDialogRef<PlanejamentoModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private fb: FormBuilder,
    private disciplinaService: DisciplinaService,
    private cicloEstudoService: CicloEstudoService,
    private planejamentoService: PlanejamentoService,
    private snackBar: MatSnackBar
  ) {
    this.initializeForms();
    this.loadDisciplinas();
  }

  private initializeForms() {
    this.step1Form = this.fb.group({
      nome: ['', Validators.required],
      horasSemanaDisponiveis: [null, [Validators.required]],
      diasSemana: this.fb.array(this.diasSemanaList.map(() => false), this.minSelectedCheckboxes(1)),
      minutosDuracaoMaximaPorSessao: [null, [Validators.required]]
    });

    this.step2Form = this.fb.group({
      disciplinas: this.fb.array([])
    });

    this.revisaoForm = this.fb.group({
      intervalos: [this.intervalosPadrao.join(','), [Validators.required]]
    });

    this.novaDisciplinaForm = this.fb.group({
      nome: ['', Validators.required]
    });
  }

  private minSelectedCheckboxes(min: number) {
    return (control: AbstractControl): ValidationErrors | null => {
      if (control instanceof FormArray) {
        const totalSelected = control.controls
          .map(ctrl => ctrl.value)
          .reduce((prev, next) => next ? prev + next : prev, 0);
        return totalSelected >= min ? null : { required: true };
      }
      return null;
    };
  }

  get disciplinasFormArray(): FormArray {
    return this.step2Form.get('disciplinas') as FormArray;
  }

  get intervalosArray(): number[] {
    const intervalosStr = this.revisaoForm.get('intervalos')?.value || '';
    return intervalosStr.split(',').map((i: string) => parseInt(i.trim())).filter((i: number) => !isNaN(i));
  }

  get isNextButtonDisabled(): boolean {
    if (this.currentStep === 2) {
      const selecionadas = this.disciplinasFormArray.controls.filter(ctrl => ctrl.value.selecionada);
      return selecionadas.length === 0;
    }
    return false;
  }

  private loadDisciplinas() {
    this.disciplinaService.getAll().subscribe({
      next: (disciplinas) => {
        this.disciplinas = disciplinas;
        this.updateDisciplinasFormArray();
      },
      error: (error) => {
        console.error('Erro ao carregar disciplinas:', error);
        this.snackBar.open('Erro ao carregar disciplinas.', 'Fechar', { duration: 3000 });
      }
    });
  }

  private updateDisciplinasFormArray() {
    const formArray = this.disciplinasFormArray;
    formArray.clear();

    if (this.disciplinas && this.disciplinas.length > 0) {
      this.disciplinas.forEach((d) => {
        const controlGroup = this.fb.group({
          selecionada: [false],
          id: [d.id],
          nome: [d.nome],
          peso: [3],
          nivelConhecimento: [3]
        });
        formArray.push(controlGroup);
      });
    }
  }

  nextStep() {
    if (this.currentStep === 1 && this.step1Form.valid) {
      this.step1Completed = true;
      this.currentStep = 2;
    } else if (this.currentStep === 2) {
      const selecionadas = this.disciplinasFormArray.controls.filter(ctrl => ctrl.value.selecionada);
      if (selecionadas.length > 0) {
        this.disciplinasSelecionadas = selecionadas.map(ctrl => ctrl.value);
        this.step2Completed = true;
        this.currentStep = 3;
        this.gerarCicloEstudo();
      } else {
        this.step2Form.markAllAsTouched();
      }
    } else if (this.currentStep === 3 && this.cicloDeEstudo) {
      this.currentStep = 4;
    }
  }

  previousStep() {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  tryNavigateToStep(step: number) {
    if (step === 1) {
      this.currentStep = 1;
    } else if (step === 2 && this.step1Completed) {
      this.currentStep = 2;
    } else if (step === 3 && this.step2Completed) {
      this.currentStep = 3;
    } else if (step === 4 && this.step3Completed) {
      this.currentStep = 4;
    }
  }

  toggleNovaDisciplinaForm() {
    this.showNovaDisciplinaForm = !this.showNovaDisciplinaForm;
    if (this.showNovaDisciplinaForm) {
      this.novaDisciplinaForm.reset();
    }
  }

  cancelarNovaDisciplina() {
    this.showNovaDisciplinaForm = false;
    this.novaDisciplinaForm.reset();
  }

  adicionarNovaDisciplina() {
    if (this.novaDisciplinaForm.valid) {
      const novaDisciplina: DisciplinaModel = {
        nome: this.novaDisciplinaForm.value.nome
      };

      this.disciplinaService.create(novaDisciplina).subscribe({
        next: (disciplinaCriada) => {
          this.disciplinas.push(disciplinaCriada);
          this.updateDisciplinasFormArray();
          this.showNovaDisciplinaForm = false;
          this.novaDisciplinaForm.reset();
          this.snackBar.open('Disciplina criada com sucesso!', 'Fechar', { duration: 3000 });
        },
        error: (error) => {
          console.error('Erro ao criar disciplina:', error);
          this.snackBar.open('Erro ao criar disciplina.', 'Fechar', { duration: 3000 });
        }
      });
    }
  }

  private gerarCicloEstudo() {
    this.loadingSugestao = true;
    this.erroSugestao = '';
    const step1 = this.step1Form.value;
    const diasSelecionados = step1.diasSemana
      .map((checked: boolean, i: number) => checked ? this.diasSemanaList[i].value : null)
      .filter((v: string | null) => v !== null);
    const disciplinasSelecionadas = this.disciplinasFormArray.controls
      .filter(ctrl => ctrl.value.selecionada)
      .map(ctrl => ({
        disciplina: { id: ctrl.value.id, nome: ctrl.value.nome },
        peso: ctrl.value.peso,
        nivelConhecimento: ctrl.value.nivelConhecimento
      }));
    const payload = {
      nome: step1.nome,
      horasSemanaDisponiveis: step1.horasSemanaDisponiveis,
      diasSemana: diasSelecionados.join(','),
      minutosDuracaoMaximaPorSessao: step1.minutosDuracaoMaximaPorSessao,
      disciplinas: disciplinasSelecionadas
    };
    this.cicloEstudoService.sugerirCicloEstudo(payload).subscribe({
      next: (res) => {
        this.cicloDeEstudo = this.garantirDadosCicloEstudo(res);
        this.loadingSugestao = false;
        this.step3Completed = true;
      },
      error: (err) => {
        this.erroSugestao = 'Erro ao gerar sugestão. Tente novamente.';
        this.loadingSugestao = false;
      }
    });
  }

  private garantirDadosCicloEstudo(ciclo: any): any {
    if (!ciclo || !ciclo.disciplinas) {
      return ciclo;
    }

    return {
      ...ciclo,
      disciplinas: ciclo.disciplinas.map((d: any) => ({
        ...d,
        cor: d.cor || this.gerarCorAleatoria(),
        tempoEstudoMeta: d.tempoEstudoMeta || '01:00'
      }))
    };
  }

  private gerarCorAleatoria(): string {
    const cores = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];
    return cores[Math.floor(Math.random() * cores.length)];
  }

  getTempoTotalCiclo(): number {
    if (!this.cicloDeEstudo || !this.cicloDeEstudo.disciplinas) {
      return 0;
    }
    return this.cicloDeEstudo.disciplinas.reduce((total: number, d: any) => {
      const tempo = d.tempoEstudoMeta || '00:00';
      const [horas, minutos] = tempo.split(':').map((t: string) => parseInt(t));
      return total + (horas * 60) + minutos;
    }, 0);
  }

  finalizarPlanejamento() {
    if (!this.cicloDeEstudo) return;

    const step1 = this.step1Form.value;
    const diasSelecionados = step1.diasSemana
      .map((checked: boolean, i: number) => checked ? this.diasSemanaList[i].value : null)
      .filter((v: string | null) => v !== null);
    const intervalos = this.intervalosArray;

    // Criar o ciclo de estudo primeiro
    const ciclo: CicloEstudoModel = {
      nome: step1.nome,
      horasSemanaDisponiveis: step1.horasSemanaDisponiveis,
      diasSemana: diasSelecionados.join(','),
      minutosDuracaoMaximaPorSessao: step1.minutosDuracaoMaximaPorSessao,
      disciplinas: this.cicloDeEstudo.disciplinas.map((d: any) => ({
        disciplina: d.disciplina,
        ordem: d.ordem,
        tempoEstudoMeta: d.tempoEstudoMeta,
        cor: d.cor,
        peso: d.peso,
        nivelConhecimento: d.nivelConhecimento
      }))
    };

    // Criar novo ciclo de estudo
    this.cicloEstudoService.create(ciclo).subscribe({
      next: (cicloSalvo) => {
        this.salvarPlanejamento(step1, diasSelecionados, intervalos, cicloSalvo);
      },
      error: (error) => {
        this.snackBar.open('Erro ao salvar ciclo de estudo. Tente novamente.', 'Fechar', { duration: 3000 });
      }
    });
  }

  private salvarPlanejamento(step1: any, diasSelecionados: string[], intervalos: number[], cicloSalvo: any) {
    const planejamento: PlanejamentoModel = {
      nome: step1.nome,
      dataCriacao: new Date(),
      horasDisponiveisPorSemana: step1.horasSemanaDisponiveis,
      minutosDuracaoMaximaPorSessao: parseInt(step1.minutosDuracaoMaximaPorSessao,10),
      diasSemanaEstudar: diasSelecionados.join(','),
      intervalosRevisao: intervalos.join(','),
      cicloEstudo: cicloSalvo
    };

    this.planejamentoService.create(planejamento).subscribe({
      next: (planejamentoSalvo) => {
        this.snackBar.open('Planejamento criado com sucesso!', 'Fechar', { duration: 3000 });
        this.dialogRef.close(true); // Retorna true para indicar sucesso
      },
      error: (error) => {
        console.error('Erro ao salvar planejamento:', error);
        this.snackBar.open('Erro ao salvar planejamento. Tente novamente.', 'Fechar', { duration: 3000 });
      }
    });
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
