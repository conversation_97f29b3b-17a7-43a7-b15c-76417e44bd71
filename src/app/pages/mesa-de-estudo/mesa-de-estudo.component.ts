import {ChangeDetectorRef, Component} from '@angular/core';
import {MatCardModule} from '@angular/material/card';
import {MatButtonModule} from '@angular/material/button';
import {MatTableModule} from '@angular/material/table';
import {MatIconModule} from '@angular/material/icon';
import {CicloEstudoService} from '../../services/ciclo-estudo.service';
import {CicloEstudoAtualModel, DisciplinaEstudoModel, RegistroEstudoModel} from '../../models/ciclo-estudo.model';
import {RevisaoEspacadaModel} from '../../models/revisao-espacada.model';
import {RevisaoEspacadaService} from '../../services/revisao-espacada.service';
import {CommonModule} from '@angular/common';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MatSelectModule} from '@angular/material/select';
import {FormsModule} from '@angular/forms';
import {MatDialog, MatDialogModule} from '@angular/material/dialog';
import {CronometroDialog} from './cronometro-dialog';
import {RegistroEstudoData, RegistroEstudoDialog} from './registro-estudo-dialog';
import {MatSnackBar} from '@angular/material/snack-bar';
import {MatChipsModule} from '@angular/material/chips';
import {DificuldadeDialog} from './dificuldade-dialog';
import {MatTabsModule} from '@angular/material/tabs';
import {RouterModule} from '@angular/router';

@Component({
  selector: 'app-mesa-de-estudo',
  standalone: true,
  imports: [
    MatCardModule,
    MatButtonModule,
    MatTableModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatFormFieldModule,
    MatSelectModule,
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatChipsModule,
    MatTabsModule,
    RouterModule
  ],
  templateUrl: './mesa-de-estudo.component.html',
  styleUrl: './mesa-de-estudo.component.css'
})
export class MesaDeEstudo {

  cicloEstudoAtual: CicloEstudoAtualModel = new CicloEstudoAtualModel();
  revisoesPendentes = new Array<RevisaoEspacadaModel>();
  revisoesHoje = new Array<RevisaoEspacadaModel>();
  revisoesProximas = new Array<RevisaoEspacadaModel>();
  revisoesPendentesCount = 0;
  activeTab = 'hoje'; // Controle das abas

  constructor(
    private cicloEstudoService: CicloEstudoService,
    private revisaoEspacadaService: RevisaoEspacadaService,
    private dialog: MatDialog,
    private cd: ChangeDetectorRef,
    private snackBar: MatSnackBar
  ) {
    this.loadCicloAtual();
    this.loadRevisoesPendentes();
  }

  // Controle das abas
  setActiveTab(tab: string, event?: Event) {
    if (event) {
      event.preventDefault();
    }
    this.activeTab = tab;
  }

  // Cálculo do progresso do círculo
  getProgressPercentage(): number {
    if (!this.cicloEstudoAtual || !this.cicloEstudoAtual.disciplinas || this.cicloEstudoAtual.disciplinas.length === 0) {
      return 0;
    }

    const totalDisciplinas = this.cicloEstudoAtual.disciplinas.length;
    const disciplinasConcluidas = this.cicloEstudoAtual.disciplinas.filter((d: any) => d.dataUltimoEstudo).length;

    return totalDisciplinas > 0 ? Math.round((disciplinasConcluidas / totalDisciplinas) * 100) : 0;
  }

  getProgressCircleOffset(): string {
    const percentage = this.getProgressPercentage();
    const circumference = 2 * Math.PI * 40; // r = 40
    const offset = circumference - (circumference * percentage) / 100;
    return offset.toString();
  }

  getProgressCircleDashArray(): string {
    const circumference = 2 * Math.PI * 40; // r = 40
    return circumference.toString();
  }

  getCurrentDiscipline(): string {
    if (!this.cicloEstudoAtual || !this.cicloEstudoAtual.disciplinas || this.cicloEstudoAtual.disciplinas.length === 0) {
      return 'Nenhuma';
    }

    const disciplinaAtual = this.cicloEstudoAtual.disciplinas.find((d: any) => !d.dataUltimoEstudo);
    return disciplinaAtual ? disciplinaAtual.nomeDisciplina : 'Concluído';
  }

  // Métodos para status das disciplinas
  getDisciplineStatus(disciplina: any): string {
    if (disciplina.dataUltimoEstudo) {
      return 'Concluído';
    }
    return 'Na fila';
  }

  getDisciplineStatusClass(disciplina: any): string {
    if (disciplina.dataUltimoEstudo) {
      return 'bg-green-100';
    }
    return 'bg-gray-100';
  }

  getDisciplineTextClass(disciplina: any): string {
    if (disciplina.dataUltimoEstudo) {
      return 'text-gray-500 line-through';
    }
    return '';
  }

  getStatusTextClass(disciplina: any): string {
    if (disciplina.dataUltimoEstudo) {
      return 'text-green-600';
    }
    return 'text-accent';
  }

  // Métodos para os novos botões
  iniciarEstudoRapido() {
    if (!this.cicloEstudoAtual || !this.cicloEstudoAtual.disciplinas) {
      this.snackBar.open('Nenhum ciclo selecionado ou sem disciplinas.', 'Fechar', {duration: 3000});
      return;
    }

    const disciplinaAtual = this.cicloEstudoAtual.disciplinas.find((d: any) => !d.dataUltimoEstudo);
    if (disciplinaAtual) {
      this.iniciarEstudo(disciplinaAtual);
    } else {
      this.snackBar.open('Todas as disciplinas já foram concluídas!', 'Fechar', {duration: 3000});
    }
  }

  registrarEstudoRapido() {
    if (!this.cicloEstudoAtual || !this.cicloEstudoAtual.disciplinas) {
      this.snackBar.open('Nenhum ciclo selecionado ou sem disciplinas.', 'Fechar', {duration: 3000});
      return;
    }

    const disciplinaAtual = this.cicloEstudoAtual.disciplinas.find((d: any) => !d.dataUltimoEstudo);
    if (disciplinaAtual) {
      this.registrarEstudo(disciplinaAtual);
    } else {
      this.snackBar.open('Todas as disciplinas já foram concluídas!', 'Fechar', {duration: 3000});
    }
  }

  iniciarEstudo(disciplinaEstudo: any) {
    const dialogRef = this.dialog.open(CronometroDialog, {
      width: '400px',
      data: { disciplinaEstudo: disciplinaEstudo }
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadCicloAtual();
        this.loadRevisoesPendentes();
      }
    });
  }

  registrarEstudo(disciplina: DisciplinaEstudoModel) {
    const dialogRef = this.dialog.open(RegistroEstudoDialog, {
      width: '500px',
      data: { disciplinaEstudo: disciplina }
    });

    dialogRef.afterClosed().subscribe((result: RegistroEstudoData) => {
      if (result) {
        const registroEstudo: RegistroEstudoModel = {
          cicloEstudoDisciplinaId: disciplina.cicloEstudoDisciplinaId,
          dataRegistro: result.dataEstudo.toISOString(),
          tempoEstudado: result.tempoEstudo,
          descricaoEstudo: result.descricao,
          gerarRevisoesEspacadas: result.gerarRevisoesEspacadas
        };

        this.cicloEstudoService.registrarEstudo(registroEstudo).subscribe({
          next: () => {
            this.snackBar.open('Registro de estudo salvo com sucesso!', 'Fechar', {duration: 3000});
            this.loadCicloAtual();
            this.loadRevisoesPendentes();
          },
          error: (error) => {
            console.error('Erro ao salvar registro de estudo:', error);
            this.snackBar.open('Erro ao salvar registro de estudo.', 'Fechar', {duration: 3000});
          }
        });
      }
    });
  }

  marcarRevisaoComoConcluida(revisao: RevisaoEspacadaModel) {
    const dialogRef = this.dialog.open(DificuldadeDialog, {
      width: '350px'
    });
    dialogRef.afterClosed().subscribe((nivelDificuldade: number) => {
      if (nivelDificuldade && !isNaN(Number(nivelDificuldade))) {
        const nivel = parseInt(nivelDificuldade as any);
        if (nivel >= 1 && nivel <= 5) {
          this.revisaoEspacadaService.marcarComoConcluida(revisao.id, nivel).subscribe({
            next: () => {
              this.snackBar.open('Revisão marcada como concluída!', 'Fechar', {duration: 3000});
              this.loadRevisoesPendentes();
            },
            error: (error) => {
              console.error('Erro ao marcar revisão como concluída:', error);
              this.snackBar.open('Erro ao marcar revisão como concluída.', 'Fechar', {duration: 3000});
            }
          });
        } else {
          this.snackBar.open('Nível de dificuldade deve ser entre 1 e 5.', 'Fechar', {duration: 3000});
        }
      }
    });
  }

  private loadCicloAtual() {
    this.cicloEstudoService.getCicloEstutosAtual().subscribe((result) => {
      this.cicloEstudoAtual = result;
      this.cd.detectChanges();
    });
  }

  private loadRevisoesPendentes() {
    this.revisaoEspacadaService.buscarTodasRevisoesPendentes().subscribe({
      next: (revisoes) => {
        this.revisoesPendentes = revisoes;
        this.separarRevisoesPorData();
        this.cd.detectChanges();
      },
      error: (error) => {
        console.error('Erro ao carregar revisões pendentes:', error);
      }
    });
  }

  private separarRevisoesPorData() {
    const hoje = new Date();
    hoje.setHours(0, 0, 0, 0);

    const amanha = new Date(hoje);
    amanha.setDate(amanha.getDate() + 1);

    this.revisoesHoje = this.revisoesPendentes.filter(revisao => {
      const dataRevisao = new Date(revisao.dataRevisao);
      dataRevisao.setHours(0, 0, 0, 0);
      return dataRevisao.getTime() === hoje.getTime();
    });

    this.revisoesProximas = this.revisoesPendentes.filter(revisao => {
      const dataRevisao = new Date(revisao.dataRevisao);
      dataRevisao.setHours(0, 0, 0, 0);
      return dataRevisao.getTime() >= amanha.getTime();
    });

    this.revisoesPendentesCount = this.revisoesHoje.length;
  }

  onTabChange(event: any) {
    // Método para lidar com mudança de aba se necessário
    console.log('Aba selecionada:', event.index);
  }

  selectCiclo() {

  }

  desvincularRegistroEstudo(disciplina: DisciplinaEstudoModel) {
    if (!disciplina || !disciplina.cicloEstudoDisciplinaId) {
      this.snackBar.open('Disciplina inválida para desvincular.', 'Fechar', {duration: 3000});
      return;
    }

    if (confirm('Deseja desvincular o registro de estudo?')) {
      this.cicloEstudoService.desvincularRegistroEstudo(disciplina.cicloEstudoDisciplinaId)
        .subscribe({
          next: () => {
            this.snackBar.open('Os registros de estudos foram desvinculados com sucesso!', 'Fechar', {duration: 3000});
            this.loadCicloAtual();
            this.loadRevisoesPendentes();
          },
          error: (error) => {
            console.error('Erro ao desvincular registro de estudo:', error);
            this.snackBar.open('Erro ao desvincular registro de estudo.', 'Fechar', {duration: 3000});
          }
        });
    }
  }

  formatarData(data: string): string {
    return new Date(data).toLocaleDateString('pt-BR');
  }

  getDificuldadeColor(nivel: number): string {
    switch (nivel) {
      case 1: return '#4caf50'; // Verde - fácil
      case 2: return '#8bc34a'; // Verde claro
      case 3: return '#ff9800'; // Laranja - médio
      case 4: return '#f44336'; // Vermelho - difícil
      case 5: return '#9c27b0'; // Roxo - muito difícil
      default: return '#757575'; // Cinza
    }
  }

  getDificuldadeText(nivel: number): string {
    switch (nivel) {
      case 1: return 'Fácil';
      case 2: return 'Muito Fácil';
      case 3: return 'Médio';
      case 4: return 'Difícil';
      case 5: return 'Muito Difícil';
      default: return 'Não definido';
    }
  }

  reiniciarCicloEstudo(cicloEstudoId: any) {
    if (!cicloEstudoId) {
      this.snackBar.open('Nenhum ciclo selecionado para reiniciar.', 'Fechar', {duration: 3000});
      return;
    }

    if (confirm('Confirma a reinicialização do ciclo de estudos?')) {
      this.cicloEstudoService.reiniciarCicloEstudo(Number(cicloEstudoId))
        .subscribe({
        next: () => {
          this.snackBar.open('Ciclo reiniciado com sucesso!','Fechar', {duration: 3000});
          this.loadCicloAtual();
        },
        error: () => {
          this.snackBar.open('Falha ao tentar reiniciar ciclo!','Fechar', {duration: 3000})
        }
        });
    }
  }

  getTempoTotalHHmmCicloEstudoSelecionado() {
    if (this.cicloEstudoAtual && this.cicloEstudoAtual.disciplinas && this.cicloEstudoAtual.disciplinas.length > 0) {
      let totalMinutos = 0;

      this.cicloEstudoAtual.disciplinas.forEach((disciplina: any) => {
        if (disciplina.tempoEstudoMeta && disciplina.tempoEstudoMeta.includes(':')) {
          const horas = parseInt(disciplina.tempoEstudoMeta.split(':')[0]);
          const minutos = parseInt(disciplina.tempoEstudoMeta.split(':')[1]);
          totalMinutos += minutos + (horas * 60);
        }
      });

      const hhMM = this.converterMinutosEmHHmm(totalMinutos);
      return `${hhMM.split(':')[0]}h ${hhMM.split(':')[1]}min`;
    }
    return '';
  }

  converterMinutosEmHHmm(minutos: number): string {
    if (minutos <= 0) {
      return '00:00';
    }

    let horas = 0;
    let minutosRestantes = minutos;
    if (minutosRestantes >= 60) {
      horas = Math.floor(minutosRestantes / 60);
      minutosRestantes = minutosRestantes % 60;
    }
    return `${horas < 10 ? '0' + horas : horas}:${minutosRestantes < 10 ? '0' + minutosRestantes : minutosRestantes}`
  }


  getTempoTotalEstudadoHHmmCicloEstudoSelecionado() {
    if (this.cicloEstudoAtual && this.cicloEstudoAtual.disciplinas && this.cicloEstudoAtual.disciplinas.length > 0) {
      let totalMinutos = 0;

      this.cicloEstudoAtual.disciplinas.forEach((disciplina: any) => {
        if (disciplina.tempoEstudado && disciplina.tempoEstudado.includes(':')) {
          const horas = parseInt(disciplina.tempoEstudado.split(':')[0]);
          const minutos = parseInt(disciplina.tempoEstudado.split(':')[1]);
          totalMinutos += minutos + (horas * 60);
        }
      });

      const hhMM = this.converterMinutosEmHHmm(totalMinutos);
      return this.formatarHoraMinutoApresentar(hhMM);
    }
    return '';
  }

  formatarHoraMinutoApresentar(tempoEstudoMeta: string) {
    if (tempoEstudoMeta && tempoEstudoMeta.includes(':')) {
      const horas = tempoEstudoMeta.split(':')[0];
      const minutos = tempoEstudoMeta.split(':')[1];
      if (horas === '00') {
        return `${minutos}min`;
      }
      return `${horas}h${minutos}min`;
    }
    return '';
  }

  getQtdDisciplinas(): number {
    if (this.cicloEstudoAtual) {
      let disciplinas: DisciplinaEstudoModel[] = [];
      this.cicloEstudoAtual.disciplinas.forEach((disciplina: DisciplinaEstudoModel) => {
        const index = disciplinas.findIndex(d => d.disciplinaId === disciplina.disciplinaId);
        if (index === -1) {
          disciplinas.push(disciplina);
        }
      });
      return disciplinas.length;
    }
    return 0;
  }

  getCiclosConcluidos(): number {
    // Valor fixo para demonstração - em produção seria consultado do backend
    return 3;
  }
}
