<div class="planejamento-modal-container">
  <h2 mat-dialog-title><PERSON><PERSON><PERSON><PERSON></h2>

  <mat-dialog-content class="modal-content">
    <!-- Barra de navegação rápida -->
    <div class="quick-navigation">
      <button mat-button
              [class.active]="currentStep === 1"
              (click)="tryNavigateToStep(1)">
        <mat-icon>info</mat-icon>
        1 - Informações
      </button>
      <button mat-button
              [class.active]="currentStep === 2"
              [class.disabled]="!step1Completed"
              (click)="tryNavigateToStep(2)">
        <mat-icon>school</mat-icon>
        2 - Disciplinas
      </button>
      <button mat-button
              [class.active]="currentStep === 3"
              [class.disabled]="!step2Completed"
              (click)="tryNavigateToStep(3)">
        <mat-icon>lightbulb</mat-icon>
        3 - <PERSON><PERSON><PERSON>
      </button>
      <button mat-button
              [class.active]="currentStep === 4"
              [class.disabled]="!step3Completed"
              (click)="tryNavigateToStep(4)">
        <mat-icon>schedule</mat-icon>
        4 - Revisões
      </button>
    </div>

    <!-- Conteúdo das etapas -->
    <div class="step-content">
      <!-- Etapa 1: Informações Iniciais -->
      <div *ngIf="currentStep === 1" class="step-panel">
        <h3>Informações Iniciais</h3>
        <form [formGroup]="step1Form">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Nome do Planejamento</mat-label>
            <input matInput formControlName="nome" required>
          </mat-form-field>
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Horas disponíveis por semana</mat-label>
            <input matInput type="number" formControlName="horasSemanaDisponiveis" required min="1">
          </mat-form-field>
          <div class="dias-semana-group">
            <label>Dias da semana para estudar:</label>
            <div class="dias-checkboxes" formArrayName="diasSemana">
              <mat-checkbox *ngFor="let dia of diasSemanaList; let i = index"
                            [formControlName]="i">
                {{ dia.label }}
              </mat-checkbox>
            </div>
          </div>
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Duração máxima por sessão (minutos)</mat-label>
            <input matInput type="number" formControlName="duracaoMaximaSessaoMinutos" required min="10">
          </mat-form-field>
        </form>
      </div>

      <!-- Etapa 2: Seleção de Disciplinas -->
      <div *ngIf="currentStep === 2" class="step-panel">
        <h3>Seleção de Disciplinas</h3>

        <!-- Formulário para nova disciplina -->
        <div *ngIf="showNovaDisciplinaForm" class="nova-disciplina-form">
          <h4>Adicionar Nova Disciplina</h4>
          <form [formGroup]="novaDisciplinaForm">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Nome da Disciplina</mat-label>
              <input matInput formControlName="nome" required>
            </mat-form-field>
            <div class="form-actions">
              <button mat-raised-button color="primary"
                      (click)="adicionarNovaDisciplina()"
                      [disabled]="!novaDisciplinaForm.valid">
                Adicionar
              </button>
              <button mat-button type="button" (click)="cancelarNovaDisciplina()">
                Cancelar
              </button>
            </div>
          </form>
        </div>

        <!-- Lista de disciplinas existentes -->
        <div *ngIf="disciplinas.length > 0" class="disciplinas-list">
          <div class="disciplinas-header">
            <h4>Disciplinas Disponíveis</h4>
            <button mat-raised-button color="accent" (click)="toggleNovaDisciplinaForm()">
              <mat-icon>add</mat-icon>
              Adicionar Disciplina
            </button>
          </div>

          <form [formGroup]="step2Form">
            <div formArrayName="disciplinas">
              <div *ngFor="let disciplinaControl of disciplinasFormArray.controls; let i = index"
                   [formGroupName]="i" class="disciplina-item">
                <div class="disciplina-checkbox">
                  <mat-checkbox formControlName="selecionada">
                    {{ disciplinaControl.get('nome')?.value }}
                  </mat-checkbox>
                </div>
                <div *ngIf="disciplinaControl.get('selecionada')?.value" class="disciplina-config">
                  <div class="config-row">
                    <mat-form-field appearance="outline">
                      <mat-label>Peso</mat-label>
                      <input matInput type="number" formControlName="peso" min="1" max="5">
                    </mat-form-field>
                    <mat-form-field appearance="outline">
                      <mat-label>Nível de Conhecimento</mat-label>
                      <input matInput type="number" formControlName="nivelConhecimento" min="1" max="5">
                    </mat-form-field>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>

        <!-- Botão para adicionar disciplina quando não há nenhuma -->
        <div *ngIf="disciplinas.length === 0 && !showNovaDisciplinaForm" class="add-disciplina-action">
          <button mat-raised-button color="primary" (click)="toggleNovaDisciplinaForm()">
            <mat-icon>add</mat-icon>
            Adicionar Primeira Disciplina
          </button>
        </div>
      </div>

      <!-- Etapa 3: Ciclo de Estudo -->
      <div *ngIf="currentStep === 3" class="step-panel">
        <h3>Ciclo de Estudo</h3>

        <!-- Loading -->
        <div *ngIf="loadingSugestao" class="loading-container">
          <mat-progress-bar mode="indeterminate"></mat-progress-bar>
          <p>Gerando sugestão de ciclo de estudos...</p>
        </div>

        <!-- Erro -->
        <div *ngIf="erroSugestao" class="erro-container">
          <p class="erro-text">{{ erroSugestao }}</p>
          <button mat-raised-button color="primary" (click)="previousStep(); nextStep()">
            Tentar Novamente
          </button>
        </div>

        <!-- Ciclo gerado -->
        <div *ngIf="cicloDeEstudo && !loadingSugestao" class="ciclo-estudo-container">
          <div class="disciplinas-ciclo">
            <div *ngFor="let d of cicloDeEstudo.disciplinas; let i = index" class="disciplina-ciclo-item">
              <div class="disciplina-header">
                <div class="ordem-badge">
                  <span class="ordem-numero">{{ d.ordem }}</span>
                </div>
                <div class="disciplina-info">
                  <h5 class="disciplina-nome">{{ d.disciplina.nome }}</h5>
                  <div class="disciplina-meta">
                    <span class="meta-label">Peso:</span>
                    <span class="meta-value peso">{{ d.peso }}</span>
                    <span class="meta-label">Nível:</span>
                    <span class="meta-value nivel">{{ d.nivelConhecimento }}</span>
                  </div>
                </div>
              </div>
              <div class="tempo-meta">
                <mat-icon>schedule</mat-icon>
                <span>{{ d.tempoEstudoMeta }}</span>
              </div>
            </div>
          </div>

          <div class="ciclo-summary">
            <div class="summary-item">
              <mat-icon>schedule</mat-icon>
              <span>Tempo total por ciclo: {{ getTempoTotalCiclo() }} min</span>
            </div>
            <div class="summary-item">
              <mat-icon>school</mat-icon>
              <span>{{ cicloDeEstudo.disciplinas.length }} disciplina(s) no ciclo</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Etapa 4: Revisões -->
      <div *ngIf="currentStep === 4" class="step-panel">
        <h3>Configuração de Revisões</h3>
        <form [formGroup]="revisaoForm">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Intervalos de Revisão (dias)</mat-label>
            <input matInput formControlName="intervalos"
                   placeholder="Ex: 1,3,7,15,30">
            <mat-hint>Separe os intervalos por vírgula</mat-hint>
          </mat-form-field>
        </form>

        <div class="intervalos-preview">
          <h4>Intervalos configurados:</h4>
          <div class="intervalos-chips">
            <span *ngFor="let intervalo of intervalosArray" class="intervalo-chip">
              {{ intervalo }} dia{{ intervalo > 1 ? 's' : '' }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end" class="modal-actions">
    <button mat-button (click)="onCancel()">Cancelar</button>
    <button *ngIf="currentStep > 1" mat-button (click)="previousStep()">Anterior</button>
    <button *ngIf="currentStep < 4"
            mat-raised-button color="primary"
            (click)="nextStep()"
            [disabled]="isNextButtonDisabled">
      Próximo
    </button>
    <button *ngIf="currentStep === 4"
            mat-raised-button color="primary"
            (click)="finalizarPlanejamento()"
            [disabled]="!step3Completed">
      Finalizar
    </button>
  </mat-dialog-actions>
</div>
