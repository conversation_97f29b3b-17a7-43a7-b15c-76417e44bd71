<div class="container-base-telas">
  <main class="flex flex-1 flex-col lg:flex-row p-6">
    <!-- Coluna principal (70%) -->
    <div class="flex-1 lg:w-[70%] space-y-6">

      <!-- Card do Ciclo de Estudos -->
      <section class="mat-card">
        <h2 class="text-xl font-medium text-primary mb-4">Ciclo de Estudos</h2>

        <!-- Indicadores do Ciclo -->
        <div *ngIf="cicloEstudoAtual.cicloEstudoId > 0" class="indicadores-ciclo mb-6">
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="indicador-item">
              <div class="indicador-valor">{{ getCiclosConcluidos() }}</div>
              <div class="indicador-label">Ciclos concluídos</div>
            </div>
            <div class="indicador-item">
              <div class="indicador-valor">{{ getTempoTotalHHmmCicloEstudoSelecionado() }}</div>
              <div class="indicador-label">Total Horas</div>
            </div>
            <div class="indicador-item">
              <div class="indicador-valor">{{ getTempoTotalEstudadoHHmmCicloEstudoSelecionado() }}</div>
              <div class="indicador-label">Total Estudou</div>
            </div>
            <div class="indicador-item">
              <div class="indicador-valor">{{ getQtdDisciplinas() }}</div>
              <div class="indicador-label">Disciplinas</div>
            </div>
          </div>
        </div>

        <!-- Estado vazio - Sem ciclos de estudo -->
        <div *ngIf="cicloEstudoAtual.cicloEstudoId === 0" class="no-ciclos">
          <p>Não há ciclos de estudo ativos.</p>
          <small>Para começar a estudar, você precisa criar um ciclo de estudo com disciplinas e metas de tempo.</small>
          <div class="mt-4">
            <button mat-raised-button color="primary" (click)="abrirModalPlanejamento()">
              <mat-icon>add</mat-icon>
              Planejar Ciclo de Estudo
            </button>
          </div>
        </div>

        <!-- Círculo de progresso e lista de disciplinas -->
        <div class="flex flex-col md:flex-row items-center gap-6" *ngIf="cicloEstudoAtual.cicloEstudoId > 0">
          <!-- Círculo de progresso -->
          <div class="circulo-progresso">
            <div>
<!--              <h2 class="text-sm font-medium text-primary mb-4" style="text-align: center">Total Ciclo: {{getTempoTotalHHmmCicloEstudoSelecionado()}}</h2>-->
<!--              <h2 class="text-sm font-medium text-primary mb-4" style="text-align: center">Total estudou: {{getTempoTotalEstudadoHHmmCicloEstudoSelecionado()}}</h2>-->
<!--              <h2 class="text-sm font-medium text-primary mb-6" style="text-align: center">Qtd Disciplinas: {{getQtdDisciplinas()}}</h2>-->
            </div>
            <svg class="w-full h-full" viewBox="0 0 100 100">
              <circle class="progress-circle-bg" cx="50" cy="50" fill="transparent" r="40" stroke-width="10"></circle>
              <circle class="progress-circle-fg" cx="50" cy="50" fill="transparent" r="40" stroke-width="10"
                      [attr.stroke-dasharray]="getProgressCircleDashArray()"
                      [style.stroke-dashoffset]="getProgressCircleOffset()"></circle>
              <text class="progress-circle-text" x="50" y="42">{{ getProgressPercentage() }}%</text>
              <text class="progress-circle-subtext" x="50" y="55">Concluído</text>
            </svg>
            <button mat-raised-button type="button" color="primary" (click)="reiniciarCicloEstudo(cicloEstudoAtual.cicloEstudoId)">Reiniciar Ciclo</button>
          </div>

          <!-- Lista de disciplinas -->
          <div class="flex-1 space-y-1 w-full">
            <div *ngFor="let disciplina of cicloEstudoAtual.disciplinas"
                 class="flex justify-between items-center p-3 rounded"
                 [ngClass]="getDisciplineStatusClass(disciplina)">
              <span class="font-medium text-sm" [ngClass]="getDisciplineTextClass(disciplina)">
                {{ disciplina.nomeDisciplina
                    + ' - ' + (disciplina.dataUltimoEstudo && disciplina.tempoEstudado
                            ? formatarHoraMinutoApresentar(disciplina.tempoEstudado) + '/' : '')
                     + (formatarHoraMinutoApresentar(disciplina.tempoEstudoMeta))
                }}
              </span>
              <div class="flex items-center gap-2">
                <span class="text-sm font-semibold flex items-center" [ngClass]="getStatusTextClass(disciplina)">
                  {{ getDisciplineStatus(disciplina) }}
                  <mat-icon *ngIf="disciplina.dataUltimoEstudo" class="text-sm ml-1 text-align-center">check_circle</mat-icon>
                </span>
                <div class="flex">
                  <button *ngIf="!disciplina.dataUltimoEstudo"
                          mat-icon-button
                          color="primary"
                          title="Iniciar Estudo"
                          (click)="iniciarEstudo(disciplina)"
                          class="action-button">
                    <mat-icon>play_arrow</mat-icon>
                  </button>
                  <button *ngIf="!disciplina.dataUltimoEstudo"
                          mat-icon-button
                          color="primary"
                          title="Registrar Estudo"
                          (click)="registrarEstudo(disciplina)">
                      <mat-icon>more_time</mat-icon>
                  </button>
                  <button *ngIf="disciplina.dataUltimoEstudo"
                          mat-icon-button
                          color="warn"
                          title="Desvincular Registro"
                          (click)="desvincularRegistroEstudo(disciplina)"
                          class="action-button">
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

    </div>

    <!-- Sidebar (30%) -->
    <aside class="lg:w-[30%] space-y-6">
      <section class="mat-card mat-card-minhas-revisoes">
        <h2 class="text-xl font-medium text-primary mb-1">Minhas Revisões</h2>

        <!-- Abas -->
        <div class="mb-4 border-b border-gray-200">
          <nav aria-label="Tabs" class="flex -mb-px">
            <a class="mat-tab-label whitespace-nowrap py-3 px-4"
               [ngClass]="activeTab === 'hoje' ? 'mat-tab-label-active' : 'border-transparent hover:text-gray-700 hover:border-gray-300'"
               href="#" (click)="setActiveTab('hoje', $event)">
              Hoje
            </a>
            <a class="mat-tab-label whitespace-nowrap py-3 px-4"
               [ngClass]="activeTab === 'proximas' ? 'mat-tab-label-active' : 'border-transparent hover:text-gray-700 hover:border-gray-300'"
               href="#" (click)="setActiveTab('proximas', $event)">
              Próximas
            </a>
          </nav>
        </div>

        <!-- Conteúdo das abas -->
        <div class="space-y-4">
          <!-- Aba Hoje -->
          <div *ngIf="activeTab === 'hoje'" class="aba-content">
            <h3 *ngIf="revisoesHoje.length > 0"  class="text-sm font-medium text-gray-500 mb-2">Hoje ({{ revisoesHoje.length }})</h3>
            <div *ngIf="revisoesHoje.length === 0" class="no-revisoes">
              <p>Nenhuma revisão pendente para hoje.</p>
              <small>As revisões aparecerão aqui quando você marcar "Gerar revisões espaçadas" ao registrar um estudo.</small>
            </div>
            <ul *ngIf="revisoesHoje.length > 0" class="space-y-3">
              <li *ngFor="let revisao of revisoesHoje"
                  class="mat-list-item flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-primary">{{ revisao.descricao }}</p>
                  <p class="text-xs text-gray-500">{{ revisao.nomeDisciplina }}</p>
                </div>
                <div class="flex items-center">
                  <mat-chip-set>
                    <mat-chip [style.background-color]="getDificuldadeColor(revisao.nivelDificuldade)"
                             [style.color]="'white'">
                      {{ getDificuldadeText(revisao.nivelDificuldade) }}
                    </mat-chip>
                  </mat-chip-set>
                  <button mat-icon-button
                          color="primary"
                          title="Marcar como revisado"
                          (click)="marcarRevisaoComoConcluida(revisao)">
                    <mat-icon>check_circle</mat-icon>
                  </button>
                </div>
              </li>
            </ul>
          </div>

          <!-- Aba Próximas -->
          <div *ngIf="activeTab === 'proximas'">
            <div *ngIf="revisoesProximas.length === 0" class="no-revisoes">
              <p>Nenhuma revisão agendada para os próximos dias.</p>
              <small>As revisões futuras aparecerão aqui conforme você registra estudos.</small>
            </div>
            <div *ngIf="revisoesProximas.length > 0" class="space-y-3">
              <div *ngFor="let revisao of revisoesProximas"
                   class="mat-list-item flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-primary">{{ revisao.descricao }}</p>
                  <p class="text-xs text-gray-500">{{ revisao.nomeDisciplina }}</p>
                  <p class="text-xs text-gray-400">{{ formatarData(revisao.dataRevisao) }}</p>
                </div>
                <div class="flex items-center">
                  <mat-chip-set>
                    <mat-chip [style.background-color]="getDificuldadeColor(revisao.nivelDificuldade)"
                             [style.color]="'white'">
                      {{ getDificuldadeText(revisao.nivelDificuldade) }}
                    </mat-chip>
                  </mat-chip-set>
                  <button mat-icon-button
                          color="primary"
                          title="Marcar como revisado"
                          (click)="marcarRevisaoComoConcluida(revisao)">
                    <mat-icon>check_circle</mat-icon>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </aside>
  </main>
</div>
