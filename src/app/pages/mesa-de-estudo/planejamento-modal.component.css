.planejamento-modal-container {
  width: 100%;
  max-width: 800px;
  min-height: 600px;
}

.modal-content {
  padding: 0;
  margin: 0;
  max-height: 70vh;
  overflow-y: auto;
}

.modal-actions {
  padding: 16px 24px;
  border-top: 1px solid #e0e0e0;
  margin: 0;
}

/* Navegação rápida */
.quick-navigation {
  display: flex;
  justify-content: space-around;
  border-bottom: 1px solid #e0e0e0;
  padding: 8px 0;
  margin-bottom: 24px;
  background-color: #f5f5f5;
}

.quick-navigation button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.quick-navigation button.active {
  background-color: #1976d2;
  color: white;
}

.quick-navigation button.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.quick-navigation button mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

/* Conteúdo das etapas */
.step-content {
  padding: 0 24px 24px 24px;
}

.step-panel {
  min-height: 400px;
}

.step-panel h3 {
  margin-bottom: 24px;
  color: #1976d2;
  font-weight: 500;
}

/* Formulários */
.full-width {
  width: 100%;
  margin-bottom: 16px;
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

/* Dias da semana */
.dias-semana-group {
  margin-bottom: 24px;
}

.dias-semana-group label {
  display: block;
  margin-bottom: 12px;
  font-weight: 500;
  color: #333;
}

.dias-checkboxes {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

/* Disciplinas */
.disciplinas-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.disciplinas-header h4 {
  margin: 0;
  color: #333;
}

.nova-disciplina-form {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.nova-disciplina-form h4 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #1976d2;
}

.disciplina-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  background-color: #fafafa;
}

.disciplina-checkbox {
  margin-bottom: 12px;
}

.disciplina-config {
  margin-left: 24px;
}

.config-row {
  display: flex;
  gap: 16px;
}

.config-row mat-form-field {
  flex: 1;
}

.add-disciplina-action {
  text-align: center;
  padding: 32px;
}

/* Ciclo de Estudo */
.loading-container {
  text-align: center;
  padding: 32px;
}

.loading-container p {
  margin-top: 16px;
  color: #666;
}

.erro-container {
  text-align: center;
  padding: 32px;
}

.erro-text {
  color: #f44336;
  margin-bottom: 16px;
}

.ciclo-estudo-container {
  margin-top: 16px;
}

.disciplinas-ciclo {
  margin-bottom: 24px;
}

.disciplina-ciclo-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 12px;
  background-color: #fafafa;
}

.disciplina-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.ordem-badge {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #1976d2;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
}

.disciplina-info {
  flex: 1;
}

.disciplina-nome {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.disciplina-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #666;
}

.meta-label {
  font-weight: 500;
}

.meta-value {
  font-weight: bold;
}

.meta-value.peso {
  color: #ff9800;
}

.meta-value.nivel {
  color: #4caf50;
}

.tempo-meta {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #1976d2;
  font-weight: 500;
}

.tempo-meta mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

.ciclo-summary {
  background-color: #e3f2fd;
  padding: 16px;
  border-radius: 8px;
  margin-top: 16px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  color: #1976d2;
  font-weight: 500;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-item mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

/* Revisões */
.intervalos-preview {
  margin-top: 24px;
}

.intervalos-preview h4 {
  margin-bottom: 12px;
  color: #333;
}

.intervalos-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.intervalo-chip {
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

/* Responsividade */
@media (max-width: 768px) {
  .planejamento-modal-container {
    max-width: 100%;
  }
  
  .quick-navigation {
    flex-direction: column;
    gap: 8px;
  }
  
  .quick-navigation button {
    flex-direction: row;
    justify-content: flex-start;
  }
  
  .config-row {
    flex-direction: column;
  }
  
  .disciplina-ciclo-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .intervalos-chips {
    justify-content: center;
  }
}
