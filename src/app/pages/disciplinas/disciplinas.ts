import {Component, OnInit} from '@angular/core';
import {FormBuilder, FormGroup, ReactiveFormsModule, Validators} from '@angular/forms';
import {MatTableModule} from '@angular/material/table';
import {MatDialogModule} from '@angular/material/dialog';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MatInputModule} from '@angular/material/input';
import {MatButtonModule} from '@angular/material/button';
import {MatIconModule} from '@angular/material/icon';
import {MatSnackBar, MatSnackBarModule} from '@angular/material/snack-bar';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import {CommonModule} from '@angular/common';
import {RouterModule} from '@angular/router';
import {DisciplinaService} from '../../services/disciplina.service';
import {DisciplinaModel} from '../../models/disciplina.model';

@Component({
  selector: 'app-disciplinas',
  standalone: true,
  imports: [
    CommonModule, RouterModule, ReactiveFormsModule,
    MatTableModule, MatDialogModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatIconModule, MatSnackBarModule, MatProgressSpinnerModule
  ],
  templateUrl: './disciplinas.html',
  styleUrl: './disciplinas.css'
})
export class Disciplinas implements OnInit {
  displayedColumns = ['id', 'nome', 'actions'];
  disciplinas: DisciplinaModel[] = [];
  loading = false;
  form: FormGroup;
  editing: DisciplinaModel | null = null;

  constructor(
    private disciplinaService: DisciplinaService,
    private fb: FormBuilder,
    private snackBar: MatSnackBar,
  ) {
    this.form = this.fb.group({
      nome: ['', Validators.required],
    });
  }

  ngOnInit() {
    this.loadDisciplinas();
  }

  loadDisciplinas() {
    this.loading = true;
    this.disciplinaService.getAll().subscribe({
      next: (data) => this.disciplinas = data,
      error: () => this.snackBar.open('Erro ao carregar disciplinas', 'Fechar', { duration: 3000 }),
      complete: () => this.loading = false
    });
  }

  submit() {
    if (this.form.invalid) return;
    const value = this.form.value;
    if (this.editing) {
      this.disciplinaService.update(this.editing.id!, value).subscribe({
        next: () => {
          this.snackBar.open('Disciplina atualizada!', 'Fechar', { duration: 3000 });
          this.loadDisciplinas();
          this.cancel();
        },
        error: () => this.snackBar.open('Erro ao atualizar', 'Fechar', { duration: 3000 })
      });
    } else {
      this.disciplinaService.create(value).subscribe({
        next: () => {
          this.snackBar.open('Disciplina criada!', 'Fechar', { duration: 3000 });
          this.loadDisciplinas();
          this.form.reset();
        },
        error: () => this.snackBar.open('Erro ao criar', 'Fechar', { duration: 3000 })
      });
    }
  }

  edit(d: DisciplinaModel) {
    this.editing = d;
    this.form.patchValue(d);
  }

  cancel() {
    this.editing = null;
    this.form.reset();
  }

  delete(id: number) {
    if (confirm('Deseja realmente excluir?')) {
      this.disciplinaService.delete(id).subscribe({
        next: () => {
          this.snackBar.open('Disciplina excluída!', 'Fechar', { duration: 3000 });
          this.loadDisciplinas();
        },
        error: () => this.snackBar.open('Erro ao excluir', 'Fechar', { duration: 3000 })
      });
    }
  }
}
