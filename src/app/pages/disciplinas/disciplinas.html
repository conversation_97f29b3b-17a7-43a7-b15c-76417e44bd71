<div class="disciplinas-container">
  <h2>Disciplinas</h2>

  <form [formGroup]="form" (ngSubmit)="submit()" class="disciplina-form">
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Nome</mat-label>
      <input matInput formControlName="nome">
      <mat-error *ngIf="form.get('nome')?.hasError('required')">Nome obrigatório</mat-error>
    </mat-form-field>
    <div class="form-actions">
      <button mat-raised-button color="primary" type="submit" [disabled]="form.invalid || loading">
        {{ editing ? 'Salvar' : 'Adicionar' }}
      </button>
      <button *ngIf="editing" mat-button type="button" (click)="cancel()">Cancelar</button>
    </div>
  </form>

  <mat-progress-spinner *ngIf="loading" mode="indeterminate" diameter="40"></mat-progress-spinner>

  <table mat-table [dataSource]="disciplinas" class="mat-elevation-z2" *ngIf="!loading && disciplinas.length">
    <ng-container matColumnDef="id">
      <th mat-header-cell *matHeaderCellDef>ID</th>
      <td mat-cell *matCellDef="let d">{{ d.id }}</td>
    </ng-container>
    <ng-container matColumnDef="nome">
      <th mat-header-cell *matHeaderCellDef>Nome</th>
      <td mat-cell *matCellDef="let d">{{ d.nome }}</td>
    </ng-container>
    <ng-container matColumnDef="actions">
      <th mat-header-cell *matHeaderCellDef>Ações</th>
      <td mat-cell *matCellDef="let d">
        <button mat-icon-button color="primary" (click)="edit(d)"><mat-icon>edit</mat-icon></button>
        <button mat-icon-button color="warn" (click)="delete(d.id!)"><mat-icon>delete</mat-icon></button>
      </td>
    </ng-container>
    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
  </table>
  <div *ngIf="!loading && !disciplinas.length" class="empty-msg">
    Nenhuma disciplina cadastrada.
  </div>
</div>
