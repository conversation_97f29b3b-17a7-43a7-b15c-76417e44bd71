.login-card {
  background: #fff;
  box-shadow: 0 4px 24px rgba(0,0,0,0.10);
  border-radius: 16px;
  max-width: 400px;
  margin: 40px auto;
  padding: 0;
  width: 100%;
}

.login-card-header {
  padding: 32px 24px 24px 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.login-title {
  font-family: 'Roboto', 'Manrope', 'Noto Sans', sans-serif;
  font-size: 2rem;
  font-weight: 500;
  color: #212121;
  margin-bottom: 24px;
  text-align: center;
}

.login-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.full-width {
  width: 100%;
}

mat-form-field.full-width {
  margin-bottom: 8px;
}

button[mat-raised-button] {
  margin-top: 8px;
  height: 48px;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 8px;
}

.login-actions {
  margin-top: 32px;
  text-align: center;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.forgot-password {
  color: #1976d2;
  font-size: 0.95rem;
  font-weight: 500;
  background: none;
  border: none;
  cursor: pointer;
  margin-bottom: 8px;
}

.register-text {
  color: #757575;
  font-size: 0.95rem;
}

.register {
  color: #1976d2;
  font-weight: 500;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0 4px;
}

@media (max-width: 600px) {
  .login-card {
    max-width: 100%;
    margin: 16px;
  }
  .login-card-header {
    padding: 20px 8px 16px 8px;
  }
}
