<mat-card class="login-card">
  <div class="login-card-header">
    <h1 class="login-title">Estudo Organizado</h1>
    <form [formGroup]="loginForm" (ngSubmit)="submit()" class="login-form">
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Email</mat-label>
        <input matInput formControlName="email" type="email" placeholder=" ">
        <mat-error *ngIf="loginForm.get('email')?.hasError('required')">Email é obrigatório</mat-error>
        <mat-error *ngIf="loginForm.get('email')?.hasError('email')">Email inválido</mat-error>
      </mat-form-field>
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Senha</mat-label>
        <input matInput formControlName="password" type="password" placeholder=" ">
        <mat-error *ngIf="loginForm.get('password')?.hasError('required')">Senha é obrigatória</mat-error>
      </mat-form-field>
      <button mat-raised-button color="primary" type="submit" [disabled]="loading || loginForm.invalid">
        {{ loading ? 'Entrando...' : 'Entrar' }}
      </button>
    </form>
    <div class="login-actions">
      <button mat-button type="button" class="forgot-password">Esqueceu sua senha?</button>
      <p class="register-text">
        Não tem uma conta ainda?
        <button mat-button type="button" class="register">Cadastre-se</button>
      </p>
    </div>
  </div>
</mat-card>
