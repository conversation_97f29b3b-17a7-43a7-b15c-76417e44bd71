<mat-card>
  <h2>Planejamento de Estudos</h2>

  <!-- Loading state -->
  <div *ngIf="loadingPlanejamento" class="loading-container">
    <mat-progress-bar mode="indeterminate"></mat-progress-bar>
    <p>Carregando planejamento...</p>
  </div>

  <!-- Exibição do planejamento existente -->
  <div *ngIf="!loadingPlanejamento && !showCriarPlanejamento && planejamentoExistente" class="planejamento-existente">
    <div class="planejamento-header">
      <h3>{{ planejamentoExistente.nome }}</h3>
      <button mat-raised-button color="primary" (click)="editarPlanejamento()">
        <mat-icon>edit</mat-icon>
        Editar Planejamento
      </button>
    </div>

    <div class="planejamento-info">
      <div class="info-item">
        <mat-icon>schedule</mat-icon>
        <div class="info-content">
          <label>Horas disponíveis por semana:</label>
          <span>{{ planejamentoExistente.horasDisponiveisPorSemana }}</span>
        </div>
      </div>

      <div class="info-item">
        <mat-icon>timer</mat-icon>
        <div class="info-content">
          <label>Duração máxima por sessão:</label>
          <span>{{ planejamentoExistente.minutosDuracaoMaximaPorSessao }}</span>
        </div>
      </div>

      <div class="info-item">
        <mat-icon>calendar_today</mat-icon>
        <div class="info-content">
          <label>Dias da semana:</label>
          <span>{{ planejamentoExistente.diasSemanaEstudar }}</span>
        </div>
      </div>

      <div class="info-item">
        <mat-icon>refresh</mat-icon>
        <div class="info-content">
          <label>Intervalos de revisão:</label>
          <span>{{ planejamentoExistente.intervalosRevisao }}</span>
        </div>
      </div>

      <div class="info-item" *ngIf="planejamentoExistente.cicloEstudo">
        <mat-icon>school</mat-icon>
        <div class="info-content">
          <label>Ciclo de estudo:</label>
          <span>{{ planejamentoExistente.cicloEstudo.nome }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Mensagem quando não há planejamento -->
  <div *ngIf="!loadingPlanejamento && !planejamentoExistente && !showCriarPlanejamento" class="sem-planejamento">
    <div class="sem-planejamento-content">
      <mat-icon class="sem-planejamento-icon">assignment</mat-icon>
      <h3>Nenhum planejamento encontrado</h3>
      <p>Você ainda não possui um planejamento de estudos. Crie um planejamento personalizado para organizar seus estudos de forma eficiente.</p>
      <button mat-raised-button color="primary" (click)="iniciarNovoPlanejamento()">
        <mat-icon>add</mat-icon>
        Criar Planejamento
      </button>
    </div>
  </div>

  <!-- Formulário de criação/edição de planejamento -->
  <div *ngIf="!loadingPlanejamento && showCriarPlanejamento">

    <!-- Botão para voltar à visualização -->
    <div class="voltar-container" *ngIf="planejamentoExistente">
      <button mat-button (click)="voltarParaVisualizacao()">
        <mat-icon>arrow_back</mat-icon>
        Voltar à visualização
      </button>
    </div>

    <!-- Barra de navegação rápida -->
    <div class="quick-navigation">
    <button mat-button
            [class.active]="currentStep === 1"
            (click)="tryNavigateToStep(1)">
      <mat-icon>info</mat-icon>
      1 - Informações
    </button>
    <button mat-button
            [class.active]="currentStep === 2"
            [class.disabled]="!step1Completed"
            (click)="tryNavigateToStep(2)">
      <mat-icon>school</mat-icon>
      2 - Disciplinas
    </button>
    <button mat-button
            [class.active]="currentStep === 3"
            [class.disabled]="!step2Completed"
            (click)="tryNavigateToStep(3)">
      <mat-icon>lightbulb</mat-icon>
      3 - Ciclo
    </button>
    <button mat-button
            [class.active]="currentStep === 4"
            [class.disabled]="!step3Completed"
            (click)="tryNavigateToStep(4)">
      <mat-icon>schedule</mat-icon>
      4 - Revisões
    </button>
  </div>

  <!-- Conteúdo das etapas -->
  <div class="step-content">
    <!-- Etapa 1: Informações Iniciais -->
    <div *ngIf="currentStep === 1" class="step-panel">
      <h3>Informações Iniciais</h3>
      <form [formGroup]="step1Form">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Nome do Planejamento</mat-label>
          <input matInput formControlName="nome" required>
        </mat-form-field>
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Horas disponíveis por semana</mat-label>
          <input matInput type="number" formControlName="horasSemanaDisponiveis" required min="1">
        </mat-form-field>
        <div class="dias-semana-group">
          <label>Dias da semana para estudar:</label>
          <div class="dias-checkboxes" formArrayName="diasSemana">
            <mat-checkbox *ngFor="let dia of diasSemanaList; let i = index" [formControlName]="i">
              {{ dia.label }}
            </mat-checkbox>
          </div>
        </div>
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Duração máxima de cada sessão estudo (minutos)</mat-label>
          <input matInput type="number" formControlName="minutosDuracaoMaximaPorSessao" required min="10">
        </mat-form-field>
        <div class="stepper-actions">
          <button mat-raised-button color="primary" (click)="nextStep()">Próximo</button>
        </div>
      </form>
    </div>

    <!-- Etapa 2: Disciplinas -->
    <div *ngIf="currentStep === 2" class="step-panel">
      <h3>Disciplinas</h3>
      <div class="disciplinas-container">
        <!-- Mensagem quando não há disciplinas -->
        <div *ngIf="disciplinas.length === 0" class="no-disciplinas">
          <p>Nenhuma disciplina cadastrada. Adicione pelo menos uma disciplina para continuar.</p>
        </div>

        <!-- Formulário para nova disciplina -->
        <div *ngIf="showNovaDisciplinaForm" class="nova-disciplina-form">
          <h4>Adicionar Nova Disciplina</h4>
          <form [formGroup]="novaDisciplinaForm">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Nome da Disciplina</mat-label>
              <input matInput formControlName="nome" required>
              <mat-error *ngIf="novaDisciplinaForm.get('nome')?.hasError('required')">
                Nome da disciplina é obrigatório
              </mat-error>
            </mat-form-field>
            <div class="form-actions">
              <button mat-raised-button color="primary"
                      (click)="criarNovaDisciplina()"
                      [disabled]="loadingNovaDisciplina || novaDisciplinaForm.invalid">
                <mat-icon *ngIf="loadingNovaDisciplina">hourglass_empty</mat-icon>
                {{ loadingNovaDisciplina ? 'Criando...' : 'Criar Disciplina' }}
              </button>
              <button mat-button type="button" (click)="cancelarNovaDisciplina()">
                Cancelar
              </button>
            </div>
          </form>
        </div>

        <!-- Lista de disciplinas existentes -->
        <div *ngIf="disciplinas.length > 0" class="disciplinas-list">
          <div class="disciplinas-header">
            <h4>Disciplinas Disponíveis</h4>
            <button mat-raised-button color="accent" (click)="toggleNovaDisciplinaForm()">
              <mat-icon>add</mat-icon>
              Adicionar Disciplina
            </button>
          </div>

          <form [formGroup]="step2Form">
            <div formArrayName="disciplinas">
              <!-- Verificação adicional -->
              <div *ngIf="disciplinasFormArray.controls.length === 0" class="no-disciplinas-form">
                <p>Nenhuma disciplina disponível no formulário. Tentando recarregar...</p>
                <p><strong>Total de disciplinas carregadas:</strong> {{ disciplinas.length }}</p>
                <p><strong>FormArray controls:</strong> {{ disciplinasFormArray.controls.length }}</p>
                <button mat-button (click)="updateDisciplinasFormArray()">Recarregar</button>
                <button mat-button (click)="loadDisciplinas()">Carregar Disciplinas</button>
              </div>

              <div *ngFor="let disciplinaCtrl of disciplinasFormArray.controls; let i = index" [formGroupName]="i" class="disciplina-item">
                <mat-checkbox formControlName="selecionada">
                  <span class="disciplina-nome">
                    {{ disciplinaCtrl.get('nome')?.value || 'Nome não disponível' }}
                  </span>
                </mat-checkbox>
                <div class="slider-group" [class.disabled]="!disciplinaCtrl.get('selecionada')?.value">
                  <div class="slider-container">
                    <label>Peso: <span class="slider-value">{{ disciplinaCtrl.get('peso')?.value }}</span></label>
                    <div class="range-slider">
                      <input
                        type="range"
                        [value]="disciplinaCtrl.get('peso')?.value"
                        (input)="onPesoChange(i, $event)"
                        [disabled]="!disciplinaCtrl.get('selecionada')?.value"
                        min="1"
                        max="5"
                        step="1"
                        class="range-input">
                      <div class="range-labels">
                        <span>1</span>
                        <span>2</span>
                        <span>3</span>
                        <span>4</span>
                        <span>5</span>
                      </div>
                    </div>
                    <div class="slider-labels">
                      <span>Baixo</span>
                      <span>Alto</span>
                    </div>
                  </div>
                  <div class="slider-container">
                    <label>Nível de Conhecimento: <span class="slider-value">{{ disciplinaCtrl.get('nivelConhecimento')?.value }}</span></label>
                    <div class="range-slider">
                      <input
                        type="range"
                        [value]="disciplinaCtrl.get('nivelConhecimento')?.value"
                        (input)="onNivelConhecimentoChange(i, $event)"
                        [disabled]="!disciplinaCtrl.get('selecionada')?.value"
                        min="1"
                        max="5"
                        step="1"
                        class="range-input">
                      <div class="range-labels">
                        <span>1</span>
                        <span>2</span>
                        <span>3</span>
                        <span>4</span>
                        <span>5</span>
                      </div>
                    </div>
                    <div class="slider-labels">
                      <span>Iniciante</span>
                      <span>Avançado</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>

        <!-- Botão para adicionar disciplina quando não há nenhuma -->
        <div *ngIf="disciplinas.length === 0 && !showNovaDisciplinaForm" class="add-disciplina-action">
          <button mat-raised-button color="primary" (click)="toggleNovaDisciplinaForm()">
            <mat-icon>add</mat-icon>
            Adicionar Primeira Disciplina
          </button>
        </div>

        <div class="stepper-actions">
          <button mat-button (click)="previousStep()">Anterior</button>
          <button mat-raised-button color="primary"
                  (click)="nextStep()"
                  [disabled]="isNextButtonDisabled">
            Próximo
          </button>
        </div>
      </div>
    </div>

    <!-- Etapa 3: Ciclo de Estudo -->
    <div *ngIf="currentStep === 3" class="step-panel">
      <div *ngIf="cicloDeEstudo" class="ciclo-estudo-container">
        <h3>Ciclo de Estudo</h3>
        <div class="disciplinas-ciclo">
          <div *ngFor="let d of cicloDeEstudo.disciplinas; let i = index" class="disciplina-ciclo-item">
            <div class="disciplina-header">
              <div class="ordem-badge">
                <span class="ordem-numero">{{ d.ordem }}</span>
              </div>
              <div class="disciplina-info">
                <h5 class="disciplina-nome">{{ d.disciplina.nome }}</h5>
                <div class="disciplina-meta">
                  <span class="meta-label">Peso:</span>
                  <span class="meta-value peso">{{ d.peso }}</span>
                  <span class="meta-label">Nível:</span>
                  <span class="meta-value nivel">{{ d.nivelConhecimento }}</span>
                </div>
              </div>
            </div>

            <div class="disciplina-config">
              <div class="config-group">
                <label class="config-label">Ordem no Ciclo:</label>
                <mat-form-field appearance="outline" class="config-input">
                  <input matInput type="number" [value]="d.ordem || 1" (input)="onOrdemChange(d, $event)" min="1" max="10">
                </mat-form-field>
              </div>

              <div class="config-group">
                <label class="config-label">Tempo por Sessão (min):</label>
                <mat-form-field appearance="outline" class="config-input">
                  <input matInput type="number" [value]="converterTempoEstudoMetaParaMinutos(d.tempoEstudoMeta)" (input)="onTempoEstudoChange(d, $event)" min="10" max="180">
                </mat-form-field>
              </div>
            </div>
          </div>
        </div>

        <div class="ciclo-summary">
          <div class="summary-item">
            <mat-icon>schedule</mat-icon>
            <span>Tempo total por ciclo: {{ getTempoTotalCiclo() }} min</span>
          </div>
          <div class="summary-item">
            <mat-icon>school</mat-icon>
            <span>{{ cicloDeEstudo.disciplinas.length }} disciplina(s) no ciclo</span>
          </div>
        </div>

        <div class="stepper-actions">
          <button mat-button (click)="previousStep()">Anterior</button>
          <button mat-raised-button color="primary" (click)="nextStep()">Próximo</button>
        </div>
      </div>
    </div>

    <!-- Etapa 4: Revisões Espaçadas -->
    <div *ngIf="currentStep === 4" class="step-panel">
      <h3>Revisões Espaçadas</h3>
      <form [formGroup]="revisaoForm">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Intervalos das revisões (dias, separados por vírgula)</mat-label>
          <input matInput formControlName="intervalos" placeholder="Ex: 1,3,7,15">
        </mat-form-field>
        <div class="intervalos-preview">
          <span>Intervalos configurados:</span>
          <span *ngFor="let i of intervalosArray; let last = last">{{i}} dia{{i > 1 ? 's' : ''}}{{!last ? ', ' : ''}}</span>
        </div>
        <div class="stepper-actions">
          <button mat-button (click)="previousStep()">Anterior</button>
          <button mat-raised-button color="primary" [disabled]="!revisaoForm.valid" (click)="finalizarPlanejamento()">Finalizar Planejamento</button>
        </div>
        <div *ngIf="sucessoFinalizacao" class="sucesso-msg">
          Planejamento salvo com sucesso!
        </div>
      </form>
    </div>
  </div>
  </div>
</mat-card>
