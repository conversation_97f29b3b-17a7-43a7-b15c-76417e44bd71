import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatStepperModule } from '@angular/material/stepper';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import {ReactiveFormsModule, FormBuilder, FormGroup, Validators, FormArray, NgModel, AbstractControl} from '@angular/forms';
import { DisciplinaService } from '../../services/disciplina.service';
import { DisciplinaModel } from '../../models/disciplina.model';
import { CicloEstudoService } from '../../services/ciclo-estudo.service';
import { CicloEstudoModel } from '../../models/ciclo-estudo.model';
import { PlanejamentoService, PlanejamentoModel } from '../../services/planejamento.service';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { FormsModule } from '@angular/forms';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-planejamento',
  standalone: true,
  imports: [
    CommonModule, MatCardModule, MatStepperModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatCheckboxModule, ReactiveFormsModule, MatProgressBarModule, FormsModule, MatSnackBarModule, MatIconModule
  ],
  templateUrl: './planejamento.component.html',
  styleUrl: './planejamento.component.css'
})
export class PlanejamentoComponent {
  step1Form: FormGroup;
  diasSemanaList = [
    { label: 'Dom', value: 'DOM' },
    { label: 'Seg', value: 'SEG' },
    { label: 'Ter', value: 'TER' },
    { label: 'Qua', value: 'QUA' },
    { label: 'Qui', value: 'QUI' },
    { label: 'Sex', value: 'SEX' },
    { label: 'Sáb', value: 'SAB' }
  ];
  disciplinas: DisciplinaModel[] = [];
  disciplinasSelecionadas: any[] = [];
  step2Form: FormGroup;
  cicloDeEstudo: any = null;
  loadingSugestao = false;
  erroSugestao = '';
  revisaoForm: FormGroup;
  intervalosPadrao: number[] = [1, 3, 7, 15];
  sucessoFinalizacao = false;

  // Novas propriedades para cadastro de disciplinas
  novaDisciplinaForm: FormGroup;
  showNovaDisciplinaForm = false;
  loadingNovaDisciplina = false;

  // Propriedades para controlar o estado das etapas
  step1Completed = false;
  step2Completed = false;
  step3Completed = false;
  currentStep = 1;

  // Propriedades para controlar o estado do planejamento
  planejamentoExistente: PlanejamentoModel | null = null;
  loadingPlanejamento = true;
  showCriarPlanejamento = false;

  constructor(
    private fb: FormBuilder,
    private disciplinaService: DisciplinaService,
    private cicloEstudoService: CicloEstudoService,
    private planejamentoService: PlanejamentoService,
    private snackBar: MatSnackBar
  ) {
    this.step1Form = this.fb.group({
      nome: ['', Validators.required],
      horasSemanaDisponiveis: [null, [Validators.required, Validators.min(1)]],
      diasSemana: this.fb.array(this.diasSemanaList.map(() => false), this.minSelectedCheckboxes(1)),
      minutosDuracaoMaximaPorSessao: [null, [Validators.required]]
    });
    this.step2Form = this.fb.group({
      disciplinas: this.fb.array([])
    });

    // Garantir que o FormArray seja inicializado corretamente
    console.log('step2Form criado:', this.step2Form);
    console.log('FormArray inicial:', this.step2Form.get('disciplinas'));

    // Verificar se o FormArray foi criado corretamente
    const disciplinasArray = this.step2Form.get('disciplinas');
    if (!disciplinasArray) {
      console.error('FormArray de disciplinas não foi criado corretamente');
      this.step2Form.setControl('disciplinas', this.fb.array([]));
    }

    // Garantir que o FormArray seja inicializado corretamente
    console.log('FormArray após verificação:', this.step2Form.get('disciplinas'));

    // Verificar se o FormArray foi criado corretamente
    const disciplinasArray2 = this.step2Form.get('disciplinas');
    if (!disciplinasArray2) {
      console.error('FormArray de disciplinas não foi criado corretamente na segunda verificação');
      this.step2Form.setControl('disciplinas', this.fb.array([]));
    }
    this.revisaoForm = this.fb.group({
      intervalos: [this.intervalosPadrao.join(','), [Validators.required]]
    });

    // Formulário para nova disciplina
    this.novaDisciplinaForm = this.fb.group({
      nome: ['', Validators.required]
    });

    this.loadDisciplinas();
    this.carregarPlanejamentoExistente();

    // Garantir que o FormArray seja inicializado
    setTimeout(() => {
      console.log('Timeout executado, disciplinas:', this.disciplinas);
      if (this.disciplinas && this.disciplinas.length > 0) {
        this.updateDisciplinasFormArray();
      } else {
        console.log('Nenhuma disciplina disponível no timeout');
        // Tentar carregar disciplinas novamente
        this.loadDisciplinas();
      }
    }, 1000);
  }

  minSelectedCheckboxes(min = 1) {
    return (control: AbstractControl) => {
      const formArray = control as FormArray;
      const totalSelected = formArray.controls
        .map(ctrl => ctrl.value)
        .reduce((prev, next) => next ? prev + 1 : prev, 0);
      return totalSelected >= min ? null : { required: true };
    };
  }

  // Método para validar se pode avançar para próxima etapa
  canProceedToStep(stepNumber: number): boolean {
    switch (stepNumber) {
      case 1: return true; // Primeira etapa sempre acessível
      case 2: return this.step1Completed;
      case 3: return this.step2Completed;
      case 4: return this.step3Completed;
      default: return false;
    }
  }

  // Método para tentar navegar para uma etapa
  tryNavigateToStep(stepNumber: number) {
    if (!this.canProceedToStep(stepNumber)) {
      const requiredStep = stepNumber - 1;
      this.snackBar.open(`Complete a etapa ${requiredStep} antes de prosseguir`, 'Fechar', { duration: 3000 });
      return false;
    }

    // Se estiver navegando para a etapa 2, garantir que o FormArray de disciplinas esteja criado
    if (stepNumber === 2) {
      this.updateDisciplinasFormArray();
    }

    this.currentStep = stepNumber;
    return true;
  }

  // Métodos de navegação
  nextStep() {
    if (this.currentStep === 1 && this.step1Form.valid) {
      this.step1Completed = true;
      this.currentStep = 2;
    } else if (this.currentStep === 2) {
      const selecionadas = this.disciplinasFormArray.controls.filter(ctrl => ctrl.value.selecionada);
      if (selecionadas.length > 0) {
        this.disciplinasSelecionadas = selecionadas.map(ctrl => ctrl.value);
        this.step2Completed = true;
        this.currentStep = 3;
        this.gerarCicloEstudo();
      } else {
        this.step2Form.markAllAsTouched();
      }
    } else if (this.currentStep === 3 && this.cicloDeEstudo) {
      this.currentStep = 4;
    }
  }

  previousStep() {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  loadDisciplinas() {
    console.log('Iniciando carregamento de disciplinas...');
    this.disciplinaService.getAll().subscribe({
      next: (disciplinas) => {
        console.log('Disciplinas recebidas do serviço:', disciplinas);
        this.disciplinas = disciplinas;
        console.log('Disciplinas atribuídas ao componente:', this.disciplinas);
        this.updateDisciplinasFormArray();
      },
      error: (error) => {
        console.error('Erro ao carregar disciplinas:', error);
        this.disciplinas = [];
        this.updateDisciplinasFormArray();
      }
    });
  }

  updateDisciplinasFormArray() {
    try {
      console.log('Iniciando updateDisciplinasFormArray...');
      console.log('step2Form existe?', !!this.step2Form);
      console.log('Disciplinas disponíveis:', this.disciplinas);

      // Garantir que o step2Form existe
      if (!this.step2Form) {
        console.error('step2Form não existe');
        return;
      }

      let formArray = this.step2Form.get('disciplinas') as FormArray;
      console.log('FormArray encontrado?', !!formArray);

      if (!formArray) {
        console.error('FormArray não encontrado, criando novo...');
        formArray = this.fb.array([]);
        this.step2Form.setControl('disciplinas', formArray);
        console.log('Novo FormArray criado');
      }

      formArray.clear();
      console.log('FormArray limpo');

      // Verificar se há disciplinas disponíveis
      if (this.disciplinas && this.disciplinas.length > 0) {
        console.log('Adicionando disciplinas ao FormArray...');
        this.disciplinas.forEach((d, index) => {
          console.log(`Adicionando disciplina ${index}:`, d);
          const controlGroup = this.fb.group({
            selecionada: [false],
            id: [d.id],
            nome: [d.nome],
            peso: [3],
            nivelConhecimento: [3]
          });
          formArray.push(controlGroup);
        });

        console.log('FormArray atualizado com sucesso:', formArray.controls.length, 'disciplinas');
        formArray.controls.forEach((control, index) => {
          console.log(`Disciplina ${index}:`, control.value);
        });
      } else {
        console.log('Nenhuma disciplina disponível para adicionar ao FormArray');
      }
    } catch (error) {
      console.error('Erro ao atualizar FormArray:', error);
    }
  }

  get disciplinasFormArray() {
    try {
      console.log('Acessando disciplinasFormArray...');
      console.log('step2Form existe?', !!this.step2Form);

      if (!this.step2Form) {
        console.error('step2Form não existe');
        return new FormArray([]);
      }

      let formArray = this.step2Form.get('disciplinas') as FormArray;
      console.log('FormArray encontrado?', !!formArray);

      if (!formArray) {
        console.error('FormArray de disciplinas não encontrado, criando novo...');
        formArray = this.fb.array([]);
        this.step2Form.setControl('disciplinas', formArray);
        console.log('Novo FormArray criado no getter');
      }
      return formArray;
    } catch (error) {
      console.error('Erro ao acessar FormArray:', error);
      return new FormArray([]);
    }
  }

  get isNextButtonDisabled() {
    return this.disciplinas.length === 0 ||
           this.disciplinasFormArray.controls.filter(ctrl => ctrl.value.selecionada).length === 0;
  }

  // Métodos para cadastro de nova disciplina
  toggleNovaDisciplinaForm() {
    this.showNovaDisciplinaForm = !this.showNovaDisciplinaForm;
    if (this.showNovaDisciplinaForm) {
      this.novaDisciplinaForm.reset();
    }
  }

  criarNovaDisciplina() {
    if (this.novaDisciplinaForm.invalid) {
      this.novaDisciplinaForm.markAllAsTouched();
      return;
    }

    this.loadingNovaDisciplina = true;
    const novaDisciplina = this.novaDisciplinaForm.value;

    this.disciplinaService.create(novaDisciplina).subscribe({
      next: (disciplinaCriada) => {
        this.snackBar.open('Disciplina criada com sucesso!', 'Fechar', { duration: 3000 });
        this.disciplinas.push(disciplinaCriada);
        this.updateDisciplinasFormArray();
        this.showNovaDisciplinaForm = false;
        this.novaDisciplinaForm.reset();
        this.loadingNovaDisciplina = false;
      },
      error: (error) => {
        this.snackBar.open('Erro ao criar disciplina. Tente novamente.', 'Fechar', { duration: 3000 });
        this.loadingNovaDisciplina = false;
      }
    });
  }

  cancelarNovaDisciplina() {
    this.showNovaDisciplinaForm = false;
    this.novaDisciplinaForm.reset();
  }

  // Métodos para gerenciar planejamento existente
  carregarPlanejamentoExistente() {
    this.loadingPlanejamento = true;
    this.planejamentoService.getByUserId().subscribe({
      next: (planejamento) => {
        if (planejamento && planejamento.id) {
          this.planejamentoExistente = planejamento;
          this.showCriarPlanejamento = false;
        } else {
          this.planejamentoExistente = null;
          this.showCriarPlanejamento = false; // Não mostrar formulário automaticamente
        }
        this.loadingPlanejamento = false;
      },
      error: (error) => {
        // Se não encontrar planejamento, não mostrar formulário automaticamente
        this.planejamentoExistente = null;
        this.showCriarPlanejamento = false;
        this.loadingPlanejamento = false;
      }
    });
  }

  iniciarNovoPlanejamento() {
    this.showCriarPlanejamento = true;
    this.currentStep = 1;
    this.step1Completed = false;
    this.step2Completed = false;
    this.step3Completed = false;
    this.cicloDeEstudo = null;
    this.step1Form.reset();
    this.step2Form.reset();
    this.revisaoForm.reset({ intervalos: this.intervalosPadrao.join(',') });

    // Recriar o FormArray de disciplinas após o reset
    this.updateDisciplinasFormArray();
  }

  editarPlanejamento() {
    if (!this.planejamentoExistente) return;

    this.showCriarPlanejamento = true;
    this.currentStep = 1;
    this.step1Completed = false;
    this.step2Completed = false;
    this.step3Completed = false;

    // Garantir que o FormArray de disciplinas esteja criado
    this.updateDisciplinasFormArray();

    // Carregar dados do planejamento existente no formulário
    this.carregarDadosPlanejamentoNoFormulario();
  }

  private carregarDadosPlanejamentoNoFormulario() {
    if (!this.planejamentoExistente) return;

    const planejamento = this.planejamentoExistente;

    // Converter tempo de volta para números
    const horasDisponiveis = planejamento.horasDisponiveisPorSemana;
    const minutosDuracaoMaxima = planejamento.minutosDuracaoMaximaPorSessao;

    // Carregar dados da etapa 1
    this.step1Form.patchValue({
      nome: planejamento.nome,
      horasSemanaDisponiveis: horasDisponiveis,
      minutosDuracaoMaximaPorSessao: minutosDuracaoMaxima
    });

    // Carregar dias da semana
    if (planejamento.diasSemanaEstudar) {
      const diasSelecionados = planejamento.diasSemanaEstudar.split(',');
      const diasArray = this.step1Form.get('diasSemana') as FormArray;
      diasArray.controls.forEach((control, index) => {
        const diaValue = this.diasSemanaList[index].value;
        control.setValue(diasSelecionados.includes(diaValue));
      });
    }

    // Carregar intervalos de revisão
    if (planejamento.intervalosRevisao) {
      this.revisaoForm.patchValue({
        intervalos: planejamento.intervalosRevisao
      });
    }

    // Se existe ciclo de estudo, carregar dados das disciplinas
    if (planejamento.cicloEstudo && planejamento.cicloEstudo.disciplinas) {
      this.cicloDeEstudo = this.garantirDadosCicloEstudo(planejamento.cicloEstudo);

      // Aguardar as disciplinas serem carregadas antes de marcar as selecionadas
      setTimeout(() => {
        this.carregarDisciplinasSelecionadas();
      }, 100);
    }

    // Marcar etapas como completadas
    this.step1Completed = true;
    if (this.cicloDeEstudo) {
      this.step2Completed = true;
      this.step3Completed = true;
    }
  }

  private carregarDisciplinasSelecionadas() {
    if (!this.cicloDeEstudo || !this.cicloDeEstudo.disciplinas) return;

    const disciplinasCiclo = this.cicloDeEstudo.disciplinas;
    const formArray = this.step2Form.get('disciplinas') as FormArray;

    formArray.controls.forEach(control => {
      const disciplinaId = control.get('id')?.value;
      const disciplinaCiclo = disciplinasCiclo.find((d: any) => d.disciplina.id === disciplinaId);

      if (disciplinaCiclo) {
        control.patchValue({
          selecionada: true,
          peso: disciplinaCiclo.peso || 3,
          nivelConhecimento: disciplinaCiclo.nivelConhecimento || 3
        });
      }
    });
  }

  voltarParaVisualizacao() {
    this.showCriarPlanejamento = false;
    this.carregarPlanejamentoExistente();
  }

  // Métodos para lidar com mudanças nos sliders
  onPesoChange(index: number, event: any) {
    const value = parseInt(event.target.value);
    const disciplinaCtrl = this.disciplinasFormArray.at(index);
    disciplinaCtrl.patchValue({ peso: value });
  }

  onNivelConhecimentoChange(index: number, event: any) {
    const value = parseInt(event.target.value);
    const disciplinaCtrl = this.disciplinasFormArray.at(index);
    disciplinaCtrl.patchValue({ nivelConhecimento: value });
  }

  gerarCicloEstudo() {
    this.loadingSugestao = true;
    this.erroSugestao = '';
    const step1 = this.step1Form.value;
    const diasSelecionados = step1.diasSemana
      .map((checked: boolean, i: number) => checked ? this.diasSemanaList[i].value : null)
      .filter((v: string | null) => v !== null);
    const disciplinasSelecionadas = this.disciplinasFormArray.controls
      .filter(ctrl => ctrl.value.selecionada)
      .map(ctrl => ({
        disciplina: { id: ctrl.value.id, nome: ctrl.value.nome },
        peso: ctrl.value.peso,
        nivelConhecimento: ctrl.value.nivelConhecimento
      }));
    const payload = {
      nome: step1.nome,
      horasSemanaDisponiveis: step1.horasSemanaDisponiveis,
      diasSemana: diasSelecionados.join(','),
      minutosDuracaoMaximaPorSessao: step1.minutosDuracaoMaximaPorSessao,
      disciplinas: disciplinasSelecionadas
    };
    this.cicloEstudoService.sugerirCicloEstudo(payload).subscribe({
      next: (res) => {
        this.cicloDeEstudo = this.garantirDadosCicloEstudo(res);
        this.loadingSugestao = false;
        this.step3Completed = true;
      },
      error: (err) => {
        this.erroSugestao = 'Erro ao gerar sugestão. Tente novamente.';
        this.loadingSugestao = false;
      }
    });
  }

  onSugestaoEditada(sugestao: any) {
    this.cicloDeEstudo = this.garantirDadosCicloEstudo(sugestao);
  }

  // Método para atualizar o tempo de estudo de uma disciplina
  onTempoEstudoChange(disciplina: any, event: any) {
    const novoValor = parseInt(event.target.value);
    if (novoValor && novoValor >= 10 && novoValor <= 180) {
      disciplina.tempoEstudoMeta = novoValor;
    }
  }

  // Método para atualizar a ordem de uma disciplina
  onOrdemChange(disciplina: any, event: any) {
    const novoValor = parseInt(event.target.value);
    if (novoValor && novoValor >= 1 && novoValor <= 10) {
      disciplina.ordem = novoValor;
    }
  }

  get intervalosArray() {
    return (this.revisaoForm.value.intervalos || '').split(',').map((v: string) => Number(v.trim())).filter((v: number) => !isNaN(v));
  }

  getTempoTotalCiclo(): number {
    if (!this.cicloDeEstudo || !this.cicloDeEstudo.disciplinas) {
      return 0;
    }
    // Garantir que os dados estejam completos antes de calcular
    const cicloProcessado = this.garantirDadosCicloEstudo(this.cicloDeEstudo);
    return cicloProcessado.disciplinas.reduce((total: number, disciplina: any) => {
      return total + this.converterTempoEstudoMetaParaMinutos(disciplina.tempoEstudoMeta);
    }, 0);
  }

  // Método para garantir que os dados do ciclo de estudo tenham valores padrão
  private garantirDadosCicloEstudo(ciclo: any): any {
    if (!ciclo || !ciclo.disciplinas) return ciclo;

    return {
      ...ciclo,
      disciplinas: ciclo.disciplinas.map((d: any) => ({
        ...d,
        tempoEstudoMeta: this.converterTempoEstudoMetaParaMinutos(d.tempoEstudoMeta),
        ordem: d.ordem || 1,
        peso: d.peso || 3,
        nivelConhecimento: d.nivelConhecimento || 3
      }))
    };
  }

  // Método para converter tempoEstudoMeta de string (HH:mm) para minutos
  converterTempoEstudoMetaParaMinutos(tempoEstudoMeta: any): number {
    if (!tempoEstudoMeta) return 30; // Valor padrão

    // Se já é um número, retorna o valor
    if (typeof tempoEstudoMeta === 'number') {
      return tempoEstudoMeta;
    }

    // Se é uma string no formato HH:mm, converte para minutos
    if (typeof tempoEstudoMeta === 'string') {
      const parts = tempoEstudoMeta.split(':');
      if (parts.length === 2) {
        const horas = parseInt(parts[0]) || 0;
        const minutos = parseInt(parts[1]) || 0;
        return (horas * 60) + minutos;
      }
    }

    // Se não conseguir converter, retorna valor padrão
    return 30;
  }

  finalizarPlanejamento() {
    if (!this.cicloDeEstudo) return;

    const step1 = this.step1Form.value;
    const diasSelecionados = step1.diasSemana
      .map((checked: boolean, i: number) => checked ? this.diasSemanaList[i].value : null)
      .filter((v: string | null) => v !== null);
    const intervalos = this.intervalosArray;

    // Criar o ciclo de estudo primeiro
    const ciclo: CicloEstudoModel = {
      nome: step1.nome,
      horasSemanaDisponiveis: step1.horasSemanaDisponiveis,
      diasSemana: diasSelecionados.join(','),
      minutosDuracaoMaximaPorSessao: step1.minutosDuracaoMaximaPorSessao,
      disciplinas: this.cicloDeEstudo.disciplinas.map((d: any) => ({
        disciplina: d.disciplina,
        ordem: d.ordem,
        tempoEstudoMeta: d.tempoEstudoMeta,
        cor: d.cor,
        peso: d.peso,
        nivelConhecimento: d.nivelConhecimento
      }))
    };

    // Verificar se é edição ou criação
    const isEdicao = !!(this.planejamentoExistente && this.planejamentoExistente.id);

    if (isEdicao) {
      // Atualizar ciclo de estudo existente
      if (this.planejamentoExistente?.cicloEstudo?.id) {
        this.cicloEstudoService.update(this.planejamentoExistente.cicloEstudo.id, ciclo).subscribe({
          next: (cicloSalvo) => {
            this.salvarPlanejamento(step1, diasSelecionados, intervalos, cicloSalvo, isEdicao);
          },
          error: (error) => {
            this.snackBar.open('Erro ao atualizar ciclo de estudo. Tente novamente.', 'Fechar', { duration: 3000 });
          }
        });
      } else {
        this.snackBar.open('Erro: ID do ciclo de estudo não encontrado.', 'Fechar', { duration: 3000 });
      }
    } else {
      // Criar novo ciclo de estudo
      this.cicloEstudoService.create(ciclo).subscribe({
        next: (cicloSalvo) => {
          this.salvarPlanejamento(step1, diasSelecionados, intervalos, cicloSalvo, isEdicao);
        },
        error: (error) => {
          this.snackBar.open('Erro ao salvar ciclo de estudo. Tente novamente.', 'Fechar', { duration: 3000 });
        }
      });
    }
  }

  private salvarPlanejamento(step1: any, diasSelecionados: string[], intervalos: number[], cicloSalvo: any, isEdicao: boolean) {
    const planejamento: PlanejamentoModel = {
      nome: step1.nome,
      dataCriacao: new Date(),
      horasDisponiveisPorSemana: step1.horasSemanaDisponiveis,
      minutosDuracaoMaximaPorSessao: step1.minutosDuracaoMaximaPorSessao,
      diasSemanaEstudar: diasSelecionados.join(','),
      intervalosRevisao: intervalos.join(','),
      cicloEstudo: cicloSalvo
    };

    // Se for edição, incluir o ID do planejamento existente
    if (isEdicao && this.planejamentoExistente) {
      planejamento.id = this.planejamentoExistente.id;
    }

    this.planejamentoService.create(planejamento).subscribe({
      next: () => {
        this.sucessoFinalizacao = true;
        const mensagem = isEdicao ? 'Planejamento atualizado com sucesso!' : 'Planejamento salvo com sucesso!';
        this.snackBar.open(mensagem, 'Fechar', { duration: 3000 });
        // Voltar para a visualização do planejamento salvo
        this.showCriarPlanejamento = false;
        this.carregarPlanejamentoExistente();
      },
      error: (error) => {
        const mensagem = isEdicao ? 'Erro ao atualizar planejamento.' : 'Erro ao salvar planejamento.';
        this.snackBar.open(mensagem + ' Tente novamente.', 'Fechar', { duration: 3000 });
      }
    });
  }

  // Métodos auxiliares para conversão de tempo
  private converterHorasParaTime(horas: number): string {
    const horasInt = Math.floor(horas);
    const minutos = Math.round((horas - horasInt) * 60);
    return `${horasInt.toString().padStart(2, '0')}:${minutos.toString().padStart(2, '0')}:00`;
  }

  private converterMinutosParaTime(minutos: number): string {
    const horas = Math.floor(minutos / 60);
    const minRestantes = minutos % 60;
    return `${horas.toString().padStart(2, '0')}:${minRestantes.toString().padStart(2, '0')}:00`;
  }

  private converterTimeParaHoras(timeString: string): number {
    if (!timeString) return 0;
    const parts = timeString.split(':');
    const horas = parseInt(parts[0]) || 0;
    const minutos = parseInt(parts[1]) || 0;
    return horas + (minutos / 60);
  }

  private converterTimeParaMinutos(timeString: string): number {
    if (!timeString) return 0;
    const parts = timeString.split(':');
    const horas = parseInt(parts[0]) || 0;
    const minutos = parseInt(parts[1]) || 0;
    return (horas * 60) + minutos;
  }
}
