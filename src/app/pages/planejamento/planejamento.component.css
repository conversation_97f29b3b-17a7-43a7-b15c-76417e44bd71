.planejamento-placeholder {
  margin-top: 24px;
  text-align: center;
}

mat-card {
  max-width: 700px;
  margin: 32px auto;
  padding: 32px;
}

.full-width {
  width: 100%;
  margin-bottom: 16px;
}

.dias-semana-group {
  margin-bottom: 16px;
}

.dias-checkboxes {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.stepper-actions {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
}

.disciplina-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  background-color: #fafafa;
}

.disciplina-nome {
  font-weight: 500;
  color: #333;
  margin-right: 8px;
}

.disciplina-id {
  color: #666;
  font-size: 12px;
}

.debug-info {
  background: #f0f0f0;
  padding: 8px;
  margin-bottom: 16px;
  font-size: 12px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.no-disciplinas-form {
  text-align: center;
  padding: 24px;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  margin-bottom: 16px;
}

.no-disciplinas-form p {
  margin: 0 0 16px 0;
  color: #856404;
  font-size: 14px;
}

.slider-group {
  display: flex;
  gap: 24px;
  margin-top: 12px;
  transition: opacity 0.3s ease;
}

.slider-group.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.slider-container {
  flex: 1;
  min-width: 200px;
}

.slider-container label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.slider-value {
  font-weight: bold;
  color: #1976d2;
  background-color: #e3f2fd;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 14px;
  min-width: 20px;
  text-align: center;
}

/* Estilos específicos para os sliders do Material */
.slider-container mat-slider {
  width: 100%;
}

/* Estilos para inputs range nativos */
.range-slider {
  margin: 8px 0;
}

.range-input {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #e0e0e0;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
  margin-bottom: 8px;
}

.range-input::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #1976d2;
  cursor: pointer;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.range-input::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #1976d2;
  cursor: pointer;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.range-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.range-input:disabled::-webkit-slider-thumb {
  background: #ccc;
  cursor: not-allowed;
}

.range-input:disabled::-moz-range-thumb {
  background: #ccc;
  cursor: not-allowed;
}

.range-labels {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.range-labels span {
  font-weight: 500;
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 4px;
  font-size: 12px;
  color: #666;
}

.slider-labels span {
  font-weight: 500;
}

.erro-msg {
  color: #f44336;
  margin-top: 8px;
  padding: 8px;
  background-color: #ffebee;
  border-radius: 4px;
}

.sucesso-msg {
  color: #4caf50;
  margin-top: 8px;
  padding: 8px;
  background-color: #e8f5e8;
  border-radius: 4px;
}

.intervalos-preview {
  margin-top: 8px;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-size: 14px;
}

/* Novos estilos para cadastro de disciplinas */
.disciplinas-container {
  padding: 16px 0;
}

.no-disciplinas {
  text-align: center;
  padding: 32px;
  background-color: #f5f5f5;
  border-radius: 8px;
  margin-bottom: 24px;
}

.no-disciplinas p {
  color: #666;
  font-size: 16px;
  margin: 0;
}

.nova-disciplina-form {
  background-color: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
}

.nova-disciplina-form h4 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #495057;
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.disciplinas-list {
  margin-bottom: 24px;
}

.disciplinas-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e0e0e0;
}

.disciplinas-header h4 {
  margin: 0;
  color: #333;
}

.add-disciplina-action {
  text-align: center;
  padding: 32px;
  background-color: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  margin-bottom: 24px;
}

/* Responsividade */
@media (max-width: 768px) {
  .slider-group {
    flex-direction: column;
    gap: 16px;
  }

  .slider-container {
    min-width: auto;
  }

  .disciplinas-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .form-actions {
    flex-direction: column;
  }

  .dias-checkboxes {
    justify-content: center;
  }
}

/* Estilos para estados do stepper */
::ng-deep .mat-step-header.mat-step-header-disabled {
  cursor: not-allowed !important;
  opacity: 0.5;
}

::ng-deep .mat-step-header.mat-step-header-disabled .mat-step-icon {
  background-color: #ccc !important;
  color: #666 !important;
}

::ng-deep .mat-step-header.mat-step-header-disabled .mat-step-label {
  color: #666 !important;
}

::ng-deep .mat-step-header.mat-step-header-disabled:hover {
  background-color: transparent !important;
}

/* Estilos para barra de navegação rápida */
.quick-navigation {
  display: flex;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.quick-navigation button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
  min-width: 100px;
}

.quick-navigation button.active {
  background-color: #1976d2;
  color: white;
}

.quick-navigation button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f5f5f5;
  color: #666;
}

.quick-navigation button:not(.disabled):not(.active):hover {
  background-color: #e3f2fd;
  color: #1976d2;
}

.quick-navigation button mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

/* Responsividade para navegação rápida */
@media (max-width: 768px) {
  .quick-navigation {
    flex-direction: column;
    gap: 4px;
  }

  .quick-navigation button {
    flex-direction: row;
    justify-content: center;
    min-width: auto;
  }
}

/* Estilos para o conteúdo das etapas */
.step-content {
  margin-top: 24px;
}

.step-panel {
  animation: fadeIn 0.3s ease-in-out;
}

.step-panel h3 {
  margin-top: 0;
  margin-bottom: 24px;
  color: #333;
  font-size: 24px;
  font-weight: 500;
}

.step-panel h4 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #333;
  font-size: 20px;
  font-weight: 500;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Estilos para a Etapa 3 - Ciclo de Estudo */
.ciclo-estudo-container {
  padding: 16px 0;
}

.ciclo-header {
  text-align: center;
  margin-bottom: 32px;
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.ciclo-header h4 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.ciclo-description {
  margin: 0;
  opacity: 0.9;
  font-size: 16px;
}

.disciplinas-ciclo {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 32px;
}

.disciplina-ciclo-item {
  background: white;
  border: 2px solid #e3f2fd;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.disciplina-ciclo-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1976d2, #42a5f5);
}

.disciplina-ciclo-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: #1976d2;
}

.disciplina-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.ordem-badge {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 4px 8px rgba(25, 118, 210, 0.3);
}

.ordem-numero {
  color: white;
  font-weight: bold;
  font-size: 18px;
}

.disciplina-info {
  flex: 1;
}

.disciplina-nome {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.disciplina-meta {
  display: flex;
  gap: 16px;
  align-items: center;
}

.meta-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.meta-value {
  padding: 4px 12px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 14px;
  min-width: 24px;
  text-align: center;
}

.meta-value.peso {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.meta-value.nivel {
  background-color: #fff3e0;
  color: #f57c00;
}

.disciplina-config {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}

.config-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-label {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.config-input {
  width: 100%;
}

.config-input ::ng-deep .mat-mdc-form-field {
  width: 100%;
}

.ciclo-summary {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid #e9ecef;
}

.ciclo-summary h4 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  color: #555;
  font-size: 16px;
}

.summary-item mat-icon {
  color: #1976d2;
  font-size: 20px;
  width: 20px;
  height: 20px;
}

/* Responsividade para a etapa 3 */
@media (max-width: 768px) {
  .disciplina-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .disciplina-meta {
    flex-wrap: wrap;
    gap: 8px;
  }

  .disciplina-config {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .ciclo-header {
    padding: 16px;
  }

  .ciclo-header h4 {
    font-size: 20px;
  }

  .ciclo-description {
    font-size: 14px;
  }
}

/* Estilos para loading state */
.loading-container {
  text-align: center;
  padding: 40px 20px;
}

.loading-container p {
  margin-top: 16px;
  color: #666;
  font-size: 16px;
}

/* Estilos para planejamento existente */
.planejamento-existente {
  padding: 24px 0;
}

.planejamento-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e3f2fd;
}

.planejamento-header h3 {
  margin: 0;
  color: #1976d2;
  font-size: 28px;
  font-weight: 600;
}

.planejamento-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.info-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  border-color: #1976d2;
}

.info-item mat-icon {
  color: #1976d2;
  font-size: 24px;
  width: 24px;
  height: 24px;
  margin-top: 2px;
}

.info-content {
  flex: 1;
}

.info-content label {
  display: block;
  font-weight: 500;
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-content span {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

/* Estilos para mensagem sem planejamento */
.sem-planejamento {
  padding: 60px 20px;
  text-align: center;
}

.sem-planejamento-content {
  max-width: 500px;
  margin: 0 auto;
}

.sem-planejamento-icon {
  font-size: 80px;
  width: 80px;
  height: 80px;
  color: #ccc;
  margin-bottom: 24px;
}

.sem-planejamento h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.sem-planejamento p {
  margin: 0 0 32px 0;
  color: #666;
  font-size: 16px;
  line-height: 1.6;
}

/* Estilos para botão voltar */
.voltar-container {
  margin-bottom: 24px;
}

.voltar-container button {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
}

.voltar-container button:hover {
  color: #1976d2;
}

/* Responsividade para planejamento existente */
@media (max-width: 768px) {
  .planejamento-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .planejamento-header h3 {
    font-size: 24px;
    text-align: center;
  }

  .planejamento-info {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .info-item {
    padding: 16px;
  }

  .info-content span {
    font-size: 16px;
  }

  .sem-planejamento {
    padding: 40px 20px;
  }

  .sem-planejamento-icon {
    font-size: 60px;
    width: 60px;
    height: 60px;
  }

  .sem-planejamento h3 {
    font-size: 20px;
  }

  .sem-planejamento p {
    font-size: 14px;
  }
}
