/* Importação da fonte Inter */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Variáveis de cores */
:host {
  --primary-blue: #4A90E2;
  --success-green: #50E3C2;
  --background-gray: #F7F7F7;
  --text-dark: #333333;
  --text-light: #666666;
  --border-light: #E0E0E0;
  --alert-orange: #F5A623;
  --white: #FFFFFF;
  --shadow-light: rgba(0, 0, 0, 0.1);
  --shadow-medium: rgba(0, 0, 0, 0.15);
}

/* Reset e configurações globais */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  margin: 0;
  padding: 0;
  background-color: var(--background-gray);
}

/* Toolbar */
.toolbar {
  background: var(--white);
  color: var(--text-dark);
  box-shadow: 0 2px 8px var(--shadow-light);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: 64px;
  display: flex;
  align-items: center;
  padding: 0 16px;
}

.menu-toggle {
  color: var(--text-dark);
  margin-right: 16px;
}

.toolbar-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-blue);
  margin-left: 8px;
}

.toolbar-spacer {
  flex: 1 1 auto;
}

/* User Menu */
.user-menu {
  display: flex;
  align-items: center;
}

.user-avatar {
  color: var(--text-dark);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  min-width: 40px;
  width: 40px;
  height: 40px;
}

.avatar-container {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--primary-blue);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.avatar-container:hover {
  background: var(--success-green);
  transform: scale(1.05);
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.avatar-icon {
  color: var(--white);
  font-size: 20px;
  width: 20px;
  height: 20px;
  line-height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* User Dropdown */
.user-dropdown {
  min-width: 200px;
  border-radius: 12px;
  box-shadow: 0 8px 24px var(--shadow-medium);
  border: 1px solid var(--border-light);
}

.user-info {
  padding: 16px;
  background: var(--background-gray);
  border-radius: 12px 12px 0 0;
}

.user-name {
  font-weight: 600;
  color: var(--text-dark);
  font-size: 14px;
  margin-bottom: 4px;
}

.user-email {
  color: var(--text-light);
  font-size: 12px;
}

/* Sidenav Container */
.sidenav-container {
  height: 100vh;
  background: var(--background-gray);
}

/* Sidenav */
.sidenav {
  width: 280px;
  background: var(--white);
  border-right: 1px solid var(--border-light);
  box-shadow: 2px 0 8px var(--shadow-light);
}

/* Sidenav Header */
.sidenav-header {
  padding: 24px 20px;
  border-bottom: 1px solid var(--border-light);
  background: linear-gradient(135deg, var(--primary-blue), var(--success-green));
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  color: var(--white);
  font-size: 28px;
}

.logo-text {
  color: var(--white);
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* Navigation List */
.nav-list {
  padding: 16px 0;
}

.nav-item {
  margin: 4px 12px;
  border-radius: 12px;
  height: 48px;
  transition: all 0.3s ease;
  color: var(--text-dark);
}

.nav-item:hover {
  background: var(--background-gray);
  transform: translateX(4px);
}

.nav-item.active-link {
  background: var(--primary-blue);
  color: var(--white);
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

.nav-item.active-link .nav-icon {
  color: var(--white);
}

.nav-icon {
  margin-right: 16px;
  color: var(--text-light);
  font-size: 20px;
  transition: color 0.3s ease;
}

.nav-text {
  font-weight: 500;
  font-size: 14px;
  letter-spacing: 0.3px;
}

/* Sidenav Footer */
.sidenav-footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  padding: 16px 20px;
  border-top: 1px solid var(--border-light);
  background: var(--white);
}

.version-info {
  text-align: center;
  color: var(--text-light);
  font-size: 12px;
  font-weight: 400;
}

/* Sidenav Content */
.sidenav-content {
  margin-top: 64px;
  background: var(--background-gray);
  min-height: calc(100vh - 64px);
}

.content-wrapper {
  /*padding: 24px;*/
  /*max-width: 1200px;*/
  margin: 0 auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidenav {
    width: 240px;
  }

  .toolbar-title {
    font-size: 16px;
  }

  .content-wrapper {
    padding: 16px;
  }

  .logo-text {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .sidenav {
    width: 100%;
  }

  .toolbar {
    padding: 0 12px;
  }

  .content-wrapper {
    padding: 12px;
  }
}

/* Animações */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.nav-item {
  animation: fadeIn 0.3s ease;
}

/* Hover effects */
.nav-item:hover .nav-icon {
  color: var(--primary-blue);
}

.nav-item.active-link:hover {
  background: var(--primary-blue);
  transform: translateX(4px);
}

/* Focus states for accessibility */
.nav-item:focus,
.user-avatar:focus,
.menu-toggle:focus {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

/* Smooth transitions */
* {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-item {
  display: flex;
  align-items: center;
}
