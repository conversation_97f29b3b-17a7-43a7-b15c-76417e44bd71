import {Component, OnInit} from '@angular/core';
import {MatToolbarModule} from '@angular/material/toolbar';
import {MatButtonModule} from '@angular/material/button';
import {MatIconModule} from '@angular/material/icon';
import {MatSidenavModule} from '@angular/material/sidenav';
import {MatListModule} from '@angular/material/list';
import {MatMenuModule} from '@angular/material/menu';
import {MatDividerModule} from '@angular/material/divider';
import {RouterModule, Router} from '@angular/router';
import {CommonModule} from '@angular/common';
import {Auth} from '../../services/auth';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';

@Component({
  selector: 'app-menu',
  standalone: true,
  imports: [
    CommonModule, 
    MatToolbarModule, 
    MatButtonModule, 
    MatIconModule, 
    MatSidenavModule, 
    MatListModule, 
    MatMenuModule, 
    MatDividerModule,
    RouterModule
  ],
  templateUrl: './menu.html',
  styleUrl: './menu.css'
})
export class Menu implements OnInit {
  sidenavOpened = true;
  userName: string | null = null;
  userPhotoUrl: SafeUrl | string = '';

  constructor(
    private auth: Auth,
    private router: Router,
    private sanitizer: DomSanitizer
  ) {}

  ngOnInit(): void {
    this.userName = this.auth.getUserName();
    const photoData = this.auth.getUserPhoto();
    if (photoData) {
      const objectURL = 'data:image/jpeg;base64,' + photoData;
      this.userPhotoUrl = this.sanitizer.bypassSecurityTrustUrl(objectURL);
    }
  }

  logout() {
    this.auth.logout();
  }

  editProfile() {
    this.router.navigate(['/profile']);
  }

  toggleSidenav() {
    this.sidenavOpened = !this.sidenavOpened;
  }
}
