<mat-toolbar class="toolbar">
  <button mat-icon-button (click)="toggleSidenav()" class="menu-toggle">
    <mat-icon>menu</mat-icon>
  </button>

  <span class="toolbar-title">Estudo Organizado</span>

  <div class="toolbar-spacer"></div>

  <div class="user-menu">
    <button mat-icon-button [matMenuTriggerFor]="userMenu" class="user-avatar">
      <div class="avatar-container">
        <img *ngIf="userPhotoUrl" [src]="userPhotoUrl" alt="User Photo" class="avatar-image">
        <mat-icon *ngIf="!userPhotoUrl" class="avatar-icon">person</mat-icon>
      </div>
    </button>

    <mat-menu #userMenu="matMenu" class="user-dropdown">
      <div class="user-info">
        <div class="user-name">{{ userName || 'Usuário' }}</div>
      </div>
      <mat-divider></mat-divider>
      <button mat-menu-item (click)="editProfile()">
        <mat-icon>edit</mat-icon>
        <span>Editar Perfil</span>
      </button>
      <button mat-menu-item (click)="logout()">
        <mat-icon>logout</mat-icon>
        <span>Sair</span>
      </button>
    </mat-menu>
  </div>
</mat-toolbar>

<mat-sidenav-container class="sidenav-container">
  <mat-sidenav #sidenav
               [mode]="'side'"
               [opened]="sidenavOpened"
               class="sidenav"
               [fixedInViewport]="true"
               [disableClose]="false">

    <div class="sidenav-header">
      <div class="logo-container">
        <mat-icon class="logo-icon">school</mat-icon>
        <span class="logo-text">Estudo Organizado</span>
      </div>
    </div>

    <mat-nav-list class="nav-list">
      <a mat-list-item
         routerLink="/dashboard"
         routerLinkActive="active-link"
         class="nav-item">
        <div class="menu-item">
          <mat-icon class="nav-icon">dashboard</mat-icon>
          <span class="nav-text">Dashboard</span>
        </div>
      </a>

      <a mat-list-item
         routerLink="/mesa-de-estudo"
         routerLinkActive="active-link"
         class="nav-item">
        <div class="menu-item">
          <mat-icon class="nav-icon">desktop_windows</mat-icon>
          <span class="nav-text">Mesa de Estudo</span>
        </div>
      </a>

      <a mat-list-item
         routerLink="/disciplinas"
         routerLinkActive="active-link"
         class="nav-item">
        <div class="menu-item">
          <mat-icon class="nav-icon">book</mat-icon>
          <span class="nav-text">Disciplinas</span>
        </div>
      </a>

    </mat-nav-list>

    <div class="sidenav-footer">
      <div class="version-info">
        <span>v1.0.0</span>
      </div>
    </div>
  </mat-sidenav>

  <mat-sidenav-content class="sidenav-content">
    <div class="content-wrapper">
      <router-outlet></router-outlet>
    </div>
  </mat-sidenav-content>
</mat-sidenav-container>
