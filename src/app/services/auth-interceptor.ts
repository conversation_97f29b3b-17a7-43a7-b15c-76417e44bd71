import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { Auth } from './auth';

export const authInterceptor: HttpInterceptorFn = (req, next) => {
  // Não incluir token em requisições de autenticação
  if (req.url.includes('/v1/auth/')) {
    return next(req);
  }

  const authService = inject(Auth);
  const token = authService.getToken(); // Isso automaticamente limpa tokens expirados
  
  if (token) {
    const authReq = req.clone({
      setHeaders: { Authorization: `Bearer ${token}` }
    });
    return next(authReq);
  }
  return next(req);
};
