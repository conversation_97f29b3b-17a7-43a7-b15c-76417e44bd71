import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { RevisaoEspacadaModel } from '../models/revisao-espacada.model';
import { environment } from '../environment';

@Injectable({
  providedIn: 'root'
})
export class RevisaoEspacadaService {

  private apiUrl = `${environment.apiUrl}/v1/revisoes-espacadas`;

  constructor(private http: HttpClient) { }

  buscarRevisoesPendentes(): Observable<RevisaoEspacadaModel[]> {
    return this.http.get<RevisaoEspacadaModel[]>(`${this.apiUrl}/pendentes`);
  }

  buscarTodasRevisoesPendentes(): Observable<RevisaoEspacadaModel[]> {
    return this.http.get<RevisaoEspacadaModel[]>(`${this.apiUrl}/todas-pendentes`);
  }

  gerarRevisoesEspacadas(registroEstudoId: number): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/gerar/${registroEstudoId}`, {});
  }

  marcarComoConcluida(revisaoId: number, nivelDificuldade: number): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/concluir/${revisaoId}?nivelDificuldade=${nivelDificuldade}`, {});
  }

  contarRevisoesPendentes(): Observable<number> {
    return this.http.get<number>(`${this.apiUrl}/contar-pendentes`);
  }
}
