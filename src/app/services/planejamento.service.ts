import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../environment';

export interface PlanejamentoModel {
  id?: number;
  nome: string;
  dataCriacao?: Date;
  horasDisponiveisPorSemana: string;
  duracaoMaximaPorSessao: string;
  diasSemanaEstudar: string;
  intervalosRevisao: string;
  cicloEstudo?: any;
}

@Injectable({
  providedIn: 'root'
})
export class PlanejamentoService {
  private apiUrl = `${environment.apiUrl}/v1/planejamento`;

  constructor(private http: HttpClient) { }

  create(planejamento: PlanejamentoModel): Observable<PlanejamentoModel> {
    if (planejamento.id) {
      // Se tem ID, é uma atualização
      return this.http.put<PlanejamentoModel>(`${this.apiUrl}/${planejamento.id}`, planejamento);
    } else {
      // Se não tem ID, é uma criação
      return this.http.post<PlanejamentoModel>(this.apiUrl, planejamento);
    }
  }

  update(id: number, planejamento: PlanejamentoModel): Observable<PlanejamentoModel> {
    return this.http.put<PlanejamentoModel>(`${this.apiUrl}/${id}`, planejamento);
  }

  getByUserId(): Observable<PlanejamentoModel> {
    return this.http.get<PlanejamentoModel>(this.apiUrl);
  }
} 