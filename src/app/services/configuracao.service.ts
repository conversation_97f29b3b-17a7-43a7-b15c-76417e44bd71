import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../environment';
import { Configuracao } from '../models/configuracao.model';

@Injectable({
  providedIn: 'root'
})
export class ConfiguracaoService {
  private apiUrl = `${environment.apiUrl}/v1/configuracoes`;

  constructor(private http: HttpClient) { }

  getConfiguration(): Observable<Configuracao> {
    return this.http.get<Configuracao>(this.apiUrl);
  }

  updateConfiguration(config: Configuracao): Observable<Configuracao> {
    return this.http.put<Configuracao>(this.apiUrl, config);
  }
} 