import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, tap } from 'rxjs';
import { environment } from '../environment';

export interface AuthResponse {
  token: string;
  name: string;
  foto: any;
}

@Injectable({
  providedIn: 'root'
})
export class Auth {
  private apiUrl = environment.apiUrl + '/v1/auth/authenticate';

  constructor(private http: HttpClient) { }

  login(email: string, password: string): Observable<AuthResponse> {
    return this.http.post<AuthResponse>(this.apiUrl, { email, password }).pipe(
      tap(response => {
        localStorage.setItem('token', response.token);
        localStorage.setItem('userName', response.name);
        if (response.foto) {
          localStorage.setItem('userPhoto', response.foto);
        }
      })
    );
  }

  logout() {
    localStorage.removeItem('token');
    localStorage.removeItem('userName');
    localStorage.removeItem('userPhoto');
    window.location.href = '/login';
  }

  getUserName(): string | null {
    return localStorage.getItem('userName');
  }

  getUserPhoto(): string | null {
    return localStorage.getItem('userPhoto');
  }

  getUserId(): number | null {
    const token = localStorage.getItem('token');
    if (!token) return null;
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.userId || payload.id || payload.sub || null;
    } catch {
      return null;
    }
  }

  isTokenExpired(): boolean {
    const token = localStorage.getItem('token');
    if (!token) return true;
    
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const expirationTime = payload.exp * 1000; // Converter para milissegundos
      return Date.now() >= expirationTime;
    } catch {
      return true;
    }
  }

  clearExpiredToken(): void {
    if (this.isTokenExpired()) {
      localStorage.removeItem('token');
      localStorage.removeItem('userName');
      localStorage.removeItem('userPhoto');
    }
  }

  getToken(): string | null {
    this.clearExpiredToken(); // Limpar token expirado automaticamente
    return localStorage.getItem('token');
  }
}
