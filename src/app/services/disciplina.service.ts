import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {environment} from '../environment';
import {DisciplinaModel} from '../models/disciplina.model';

@Injectable({
  providedIn: 'root'
})
export class DisciplinaService {
  private apiUrl = environment.apiUrl + '/v1/disciplinas';

  constructor(private http: HttpClient) { }

  getAll(): Observable<DisciplinaModel[]> {
    return this.http.get<DisciplinaModel[]>(this.apiUrl);
  }

  getById(id: number): Observable<DisciplinaModel> {
    return this.http.get<DisciplinaModel>(`${this.apiUrl}/${id}`);
  }

  create(disciplina: DisciplinaModel): Observable<DisciplinaModel> {
    return this.http.post<DisciplinaModel>(this.apiUrl, disciplina);
  }

  update(id: number, disciplina: DisciplinaModel): Observable<DisciplinaModel> {
    return this.http.put<DisciplinaModel>(`${this.apiUrl}/${id}`, disciplina);
  }

  delete(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
}
