import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { UserProfile, UserProfileUpdate } from '../models/user-profile.model';
import { environment } from '../environment';

@Injectable({
  providedIn: 'root'
})
export class UserProfileService {
  private apiUrl = `${environment.apiUrl}/api/profile`;

  constructor(private http: HttpClient) { }

  getUserProfile(): Observable<UserProfile> {
    return this.http.get<UserProfile>(this.apiUrl);
  }

  updateUserProfile(profileUpdate: UserProfileUpdate, photo?: File): Observable<UserProfile> {
    const formData = new FormData();

    // O DTO deve ser enviado como um Blob para que o backend possa desserializá-lo corretamente.
    const profileBlob = new Blob([JSON.stringify(profileUpdate)], { type: 'application/json' });
    formData.append('profile', profileBlob);

    if (photo) {
      formData.append('photo', photo, photo.name);
    }
    
    return this.http.put<UserProfile>(this.apiUrl, formData);
  }
} 